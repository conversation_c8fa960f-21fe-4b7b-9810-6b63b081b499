<?php

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementAprovedCriteria;
use App\Entity\AnnouncementCriteria;
use Doctrine\ORM\EntityManagerInterface;


class AnnouncementAprovedCriteriaService
{
    public const PERCENTAGE_DEFAULT = 70;
    private const CRITERIA_TYPES = [
        "MINIMUM_GRADE_TO_PASS_CHAPTERS" => 1, // Nota mínima para superar capitulos de evaluación
        "COMPLETE_TASKS" => 2, // Completar tareas
        "MAXIMUM_INACTIVITY_TIME" => 3, // Tiempo máximo de inactividad
        "HOURS_OF_COMPLETED_TRAINING" => 5, // Horas de formación completadas
    ];

    private $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function fetchAprovedCriteria(Announcement $announcement, AnnouncementCriteria $announcementCriteria)
    {

        $announcementAprovedCriteria = $this->em->getRepository(AnnouncementAprovedCriteria::class)
            ->findOneBy([
                'announcement' => $announcement,
                'announcementCriteria' => $announcementCriteria
            ]);

        return $announcementAprovedCriteria;
    }

    public function fetchTypeAprovedCriteria($id)
    {

        return $this->em->getRepository(AnnouncementCriteria::class)->find($id);
    }

    private function getAprovedCriteriaValue(Announcement $announcement, string $criteriaType)
    {
        $aproveCriteria = $this->fetchTypeAprovedCriteria($criteriaType);
        $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $aproveCriteria);

        if ($announcementAprovedCriteria !== null) {
            return $announcementAprovedCriteria->getValue();
        }

        return self::PERCENTAGE_DEFAULT;
    }

    public function getAnnouncementAprovedCriteria(Announcement $announcement): array
    {
        $aprovedCriteriasValues = [];
        $announcementsAprovedCriteria = $this->em->getRepository(AnnouncementAprovedCriteria::class)
            ->findBy(['announcement' => $announcement]);

        foreach ($announcementsAprovedCriteria as $announcementAprovedCriteria) {

            $aprovedCriteriasValues[] = [
                'id' => $announcementAprovedCriteria->getId(),
                'name' => $announcementAprovedCriteria->getAnnouncementCriteria()->getName(),
                'value' => $announcementAprovedCriteria->getValue() ?? self::PERCENTAGE_DEFAULT,
                'criteriaTypeKey' => $announcementAprovedCriteria->getAnnouncementCriteria()->getId(),
            ];
        }

        return $aprovedCriteriasValues;
    }

    public function hasMinimumGradeToPassChapters(Announcement $announcement)
    {

        $minimumGradeToPassChapters = $this->fetchTypeAprovedCriteria(self::CRITERIA_TYPES['MINIMUM_GRADE_TO_PASS_CHAPTERS']);

        $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $minimumGradeToPassChapters);
        if ($announcementAprovedCriteria === null) return false;

        return  $minimumGradeToPassChapters === $announcementAprovedCriteria->getAnnouncementCriteria();
    }

    public function getValueMinimiunGradeToPassChapters(Announcement $announcement)
    {
        return $this->getAprovedCriteriaValue($announcement, self::CRITERIA_TYPES['MINIMUM_GRADE_TO_PASS_CHAPTERS']);
    }

    public function hasCompleteTasks(Announcement $announcement)
    {
        $completeTasks = $this->fetchTypeAprovedCriteria(self::CRITERIA_TYPES['COMPLETE_TASKS']);
        $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $completeTasks);
        if ($announcementAprovedCriteria === null) return false;

        return  $completeTasks === $announcementAprovedCriteria->getAnnouncementCriteria();
    }

    public function getValueCompleteTasks(Announcement $announcement)
    {
        return $this->getAprovedCriteriaValue($announcement, self::CRITERIA_TYPES['COMPLETE_TASKS']);
    }

    public function hasMaximumInactivityTime(Announcement $announcement)
    {
        $maximumInactivityTime = $this->fetchTypeAprovedCriteria(self::CRITERIA_TYPES['MAXIMUM_INACTIVITY_TIME']);
        $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $maximumInactivityTime);
        if ($announcementAprovedCriteria === null) return false;

        return  $maximumInactivityTime  === $announcementAprovedCriteria->getAnnouncementCriteria();
    }
    public function getValueMaximumInactivityTime(Announcement $announcement)
    {
        return $this->getAprovedCriteriaValue($announcement, self::CRITERIA_TYPES['MAXIMUM_INACTIVITY_TIME']);
    }

    public function hasHoursOfCompletedTraining(Announcement $announcement)
    {
        $hoursOfCompletedTraining = $this->fetchTypeAprovedCriteria(self::CRITERIA_TYPES['HOURS_OF_COMPLETED_TRAINING']);
        $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $hoursOfCompletedTraining);

        if ($announcementAprovedCriteria === null) return false;

        return  $hoursOfCompletedTraining  === $announcementAprovedCriteria->getAnnouncementCriteria();
    }

    public function getValueHoursOfCompletedTraining(Announcement $announcement)
    {
        return $this->getAprovedCriteriaValue($announcement, self::CRITERIA_TYPES['HOURS_OF_COMPLETED_TRAINING']);
    }

    public function getPercentageForAprovedAnnoucement(Announcement $announcement)
    {
        $value = 0;
        $count = 0;

        if ($this->hasMinimumGradeToPassChapters($announcement)) {
            $value += $this->getValueMinimiunGradeToPassChapters($announcement);
            $count++;
        }

        if ($this->hasCompleteTasks($announcement)) {
            $value += $this->getValueCompleteTasks($announcement);
            $count++;
        }

        if ($this->hasHoursOfCompletedTraining($announcement)) {
            $value += $this->getValueHoursOfCompletedTraining($announcement);
            $count++;
        }

        if ($count > 0) {
            $value /= $count;
        }

        return $count > 0 ? $value : self::PERCENTAGE_DEFAULT;
    }

    public function hasAprovedCriteria(Announcement $announcement)
    {

        foreach (self::CRITERIA_TYPES as  $value) {
            $announcementCriteria = $this->fetchTypeAprovedCriteria($value);
            $announcementAprovedCriteria = $this->fetchAprovedCriteria($announcement, $announcementCriteria);

            if ($announcementAprovedCriteria !== null) {
                return true;
            }
        }

        return false;
    }
}
