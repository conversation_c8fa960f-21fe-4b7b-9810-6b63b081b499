<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\TypeCourse;
use App\Entity\UserCourse;
use App\Entity\UserHistoryDownloadDiploma;
use App\Entity\UserTime;
use App\Service\SettingsService;
use App\Service\Traits\Announcement\AnnouncementUserAlertTrait;
use App\Service\Traits\Announcement\AssistanceBySessionTrait;
use App\Utils\TimeUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;

class AnnouncementUserService
{
    use AnnouncementUserAlertTrait;
    use AssistanceBySessionTrait;

    private $em;
    private $announcementConfigurationsService;
    private $annnouncementAlertTutorService;
    private $taskUserService;
    private $userChapterService;
    private $announcementAprovedCriteriaService;
    private $settings;

    public function __construct(
        EntityManagerInterface $em,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        AnnouncementAlertTutorService $annnouncementAlertTutorService,
        TaskUserService $taskUserService,
        UserChapterService $userChapterService,
        AnnouncementAprovedCriteriaService $announcementAprovedCriteriaService,
        SettingsService $settings
    ) {
        $this->em = $em;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->annnouncementAlertTutorService = $annnouncementAlertTutorService;
        $this->taskUserService = $taskUserService;
        $this->userChapterService = $userChapterService;
        $this->announcementAprovedCriteriaService = $announcementAprovedCriteriaService;
        $this->settings = $settings;
    }

    public function confirmDownloadedDiploma(AnnouncementUser $announcementUser)
    {
        $userHistoryDownloadDiploma = $this->em->getRepository(UserHistoryDownloadDiploma::class)
            ->findOneBy(
                [
                    'announcement' => $announcementUser->getAnnouncement()->getId(),
                    'createdBy' => $announcementUser->getUser()->getId(),
                ]
            );

        return null !== $userHistoryDownloadDiploma;
    }

    // Ha completado todos lo capítulos del curso
    public function markChapterAsCompleted(AnnouncementUser $announcementUser)
    {
        $userCourse = $this->getUserCourse($announcementUser);

        return null !== $userCourse && null !== $userCourse->getFinishedAt();
    }

    public function hasSurveyCompleted(AnnouncementUser $announcementUser)
    {
        $isSurveyAnnouncement = $this->announcementConfigurationsService->hasSurvey($announcementUser->getAnnouncement());

        if (!$isSurveyAnnouncement) {
            return true;
        }

        return $announcementUser->getValuedCourseAt() && $announcementUser->getDateApproved() ? true : false;
    }

    private function getUserCourse(AnnouncementUser $announcementUser)
    {
        $user = $announcementUser->getUser();
        $announcement = $announcementUser->getAnnouncement();

        return $this->em->getRepository(UserCourse::class)
            ->findOneBy(['user' => $user, 'announcement' => $announcement]);
    }

    public function getConexionsAnnouncementUser(AnnouncementUser $announcementUser, bool $complete = false): array
    {
        $conexions = $this->getConexionsUserTime($announcementUser, $complete);
        $conexionsInUserCourseChapter = $this->userChapterService->getConexionsAndTimeUserCourseChapter($announcementUser->getUser(), $announcementUser->getAnnouncement(), $complete);

        $conexions = array_merge($conexions, $conexionsInUserCourseChapter);

        if (!$complete) {
            $groupedConexions = $this->groupConnectionsByTime($conexions);
        } else {
            $groupedConexions = $conexions;
        }

        $resultConnections = array_values($groupedConexions);

        $filterConnections = [];

        if ($announcementUser->getAnnouncement()->getFinishAt()) {
            foreach ($resultConnections as $resultConnection) {
                $timezone = $announcementUser->getAnnouncement()->getTimezone() ?? $this->settings->get('app.default_timezone');
                $tz = new \DateTimeZone($timezone);
                $convertedDate = \DateTimeImmutable::createFromFormat('d/m/Y', $resultConnection['day'], $tz);
                if ($convertedDate->format('Y-m-d') <= $announcementUser->getAnnouncement()->getFinishAt()->format('Y-m-d')) {
                    $filterConnections[] = $resultConnection;
                }
            }
        } else {
            $filterConnections = $resultConnections;
        }

        return $filterConnections;
    }

    private function getConexionsUserTime(AnnouncementUser $announcementUser, bool $complete = false): array
    {
        $user = $announcementUser->getUser();

        $conexionsUserTime = $this->em->getRepository(UserTime::class)
            ->findBy(['user' => $user, 'announcement' => $announcementUser->getAnnouncement()]);

        $conexions = [];

        foreach ($conexionsUserTime as $conexion) {
            $time = $conexion->getTime() ?? 0;

            if (0 === $time) {
                continue;
            }

            $updatedAt = $conexion->getUpdatedAt();
            $createdAt = $updatedAt->sub(new \DateInterval('PT' . $time . 'S'));

            $conexions[] = array_merge(
                [
                    'updatedAt' => $createdAt,
                    'day' => $createdAt->format('d/m/Y'),
                    'ip' => $conexion->getIp(),
                    'time' => $time,
                    'initFin' => $createdAt->format('H:i:s'),
                ],
                $complete ? [
                    'id' => $conexion->getId(),
                    'user_id' => $user->getId(),
                    'user_code' => $user->getCode(),
                    'user_firstname' => $user->getFirstName(),
                    'user_lastname' => $user->getLastName(),
                    'user_email' => $user->getEmail(),
                ] : []
            );
        }

        return $conexions;
    }

    private function groupConnectionsByTime(array $conexions): array
    {
        $groupedConexions = [];

        foreach ($conexions as $conexion) {
            $time = $conexion['time'] ?? 0;

            if (0 === $time) {
                continue;
            }

            $createdAt = $conexion['updatedAt'];
            $dayHourKey = $createdAt->format('d/m/Y H:00:00'); // Grouping by hour in this example

            if (!isset($groupedConexions[$dayHourKey])) {
                $groupedConexions[$dayHourKey] = [
                    'updatedAt' => $conexion['updatedAt'],
                    'day' => $conexion['day'],
                    'ip' => $conexion['ip'],
                    'time' => 0,
                    'initFin' => $conexion['initFin'],
                ];
            }

            $groupedConexions[$dayHourKey]['time'] += $time;
        }

        foreach ($groupedConexions as $key => $groupedConexion) {
            $groupedConexions[$key]['time'] = TimeUtils::formatTime($groupedConexion['time']);
            $groupedConexions[$key]['initFin'] = TimeUtils::addSecondsToTime($groupedConexion['initFin'], $groupedConexion['time']);
        }

        return $groupedConexions;
    }

    public function getTotalNotificationByType(AnnouncementUser $announcementUser)
    {
        // TODO: Se tiene que realizar un cálculo correspodiente para mostrar el total de notificaciones por tipo
        $user = $announcementUser->getUser();
        $announcement = $announcementUser->getAnnouncement();

        return [
            'tasks' => $this->taskUserService->fetchTaskAnnouncement($user, $announcement),
            'chapters' => $this->userChapterService->getProgressUserByChapterForAnnoucement($announcementUser->getUser(), $announcement),
            'messages' => [],
            'notifications' => [],
            'alerts' => array_values($this->getAlertsUser($announcementUser)),
        ];
    }

    public function getProgressUserCourse(AnnouncementUser $announcementUser)
    {
        $progress = 0;
        $userCourse = $this->getUserCourse($announcementUser);

        if (null !== $userCourse) {
            $progress = $userCourse->getProgress();
        }

        return $progress;
    }

    public function getProgressUserTask(AnnouncementUser $announcementUser)
    {
        return $this->taskUserService->getProgressUserTask($announcementUser->getUser(), $announcementUser->getAnnouncement());
    }

    public function getProgressTotal(AnnouncementUser $announcementUser)
    {
        if (TypeCourse::TYPE_PRESENCIAL == $announcementUser->getAnnouncement()->getCourse()->getTypeCourse()->getId() || TypeCourse::TYPE_AULA_VIRTUAL == $announcementUser->getAnnouncement()->getCourse()->getTypeCourse()->getId()) {
            return $this->getProgressTotalAssistance($announcementUser);
        }

        $progressCourse = $this->getProgressUserCourse($announcementUser);
        $progressTask = $this->getProgressUserTask($announcementUser);
        $progressTotalHours = $this->getProgressTotalHour($announcementUser);

        if ($this->hasAnnouncementTasks($announcementUser) && !$this->announcementAprovedCriteriaService->hasHoursOfCompletedTraining($announcementUser->getAnnouncement())) {
            return round(($progressCourse + $progressTask) / 2);
        }

        if (!$this->hasAnnouncementTasks($announcementUser) && $this->announcementAprovedCriteriaService->hasHoursOfCompletedTraining($announcementUser->getAnnouncement())) {
            return round(($progressCourse + $progressTask) / 2);
        }

        if ($this->hasAnnouncementTasks($announcementUser) && $this->announcementAprovedCriteriaService->hasHoursOfCompletedTraining($announcementUser->getAnnouncement())) {
            return round(($progressCourse + $progressTask + $progressTotalHours) / 3);
        }

        return $progressCourse;
    }

    private function hasAnnouncementTasks(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        $user = $announcementUser->getUser();

        $tasks = $this->taskUserService->getTaskAnnouncementGroup($announcement, $user);

        return \count($tasks) > 0;
    }

    /**
     * @param ChatChannel|null $chatChannel Main direct channel
     *
     * @throws NonUniqueResultException
     */
    public function getUserDirectChannel(AnnouncementUser $announcementUser, ?ChatChannel $chatChannel = null): ?int
    {
        $user = $announcementUser->getUser();
        /** @var ChatChannel|null $channel */
        $channel = $this->em->getRepository(ChatChannel::class)->findChannelByUserAndParent($user, $chatChannel);

        return $channel ? $channel->getId() : null;
    }

    public function getTotalTimeConexions(AnnouncementUser $announcementUser)
    {
        $conexions = $this->em->getRepository(UserTime::class)
            ->findBy(['user' => $announcementUser->getUser(), 'announcement' => $announcementUser->getAnnouncement()]);

        if (null === $conexions) {
            return 0;
        }

        $timeTotal = 0;
        foreach ($conexions as $conexion) {
            $timeTotal += $conexion->getTime() ?? 0;
        }

        return $timeTotal;
    }

    public function getTotalInTheCourse(AnnouncementUser $announcementUser)
    {
        $totalTimeContent = $this->userChapterService->getTotalTimeUserCourseContent($announcementUser->getUser(), $announcementUser->getAnnouncement());
        $totalTimeConexions = $this->getTotalTimeConexions($announcementUser);

        return TimeUtils::formatTime($totalTimeContent + $totalTimeConexions);
    }

    private function getConexion($announcementUser, $orderBy = 'ASC')
    {
        $order = ('ASC' === $orderBy) ? 'ASC' : 'DESC';

        $conexion = $this->em->getRepository(UserTime::class)
            ->findOneBy(
                ['user' => $announcementUser->getUser(), 'announcement' => $announcementUser->getAnnouncement()],
                ['updatedAt' => $order]
            );

        return $conexion ?? null;
    }

    public function getFirstConexion($announcementUser)
    {
        return $this->getConexion($announcementUser, 'ASC');
    }

    public function getLastConexion($announcementUser)
    {
        return $this->getConexion($announcementUser, 'DESC');
    }

    public function getProgressTotalHour(AnnouncementUser $announcementUser)
    {
        if ($announcementUser->getAnnouncement()->getTotalHours() > 0) {
            $totalTimeConexions = $this->getTotalTimeConexions($announcementUser);
            $totalTimeContent = $this->userChapterService->getTotalTimeUserCourseContent($announcementUser->getUser(), $announcementUser->getAnnouncement());

            $totalTime = $totalTimeConexions + $totalTimeContent;
            $hoursAnnouncement = $announcementUser->getAnnouncement()->getTotalHours() * 3600;
            $totalTimeAnnouncement = $hoursAnnouncement;

            if ($totalTime > $totalTimeAnnouncement) {
                return 100;
            }

            if ($totalTime > 0) {
                return round(($totalTime / $totalTimeAnnouncement) * 100);
            }

            return 0;
        }

        return 100;
    }

    // Esto permite obtener el progreso de los criterios aprobados de cada usuario
    public function getProgressAprovedCriteria(AnnouncementUser $announcementUser)
    {
        $progressUserCourse = $this->getProgressTotal($announcementUser) ?? 0;
        $progressHours = 0; // Valor por defecto

        $announcement = $announcementUser->getAnnouncement();

        if ($this->announcementAprovedCriteriaService->hasHoursOfCompletedTraining($announcement)) {
            $progressHours = $this->getProgressTotalHour($announcementUser) ?? 0;
            $progressTotal = $progressUserCourse + $progressHours;

            return round($progressTotal / 2);
        }

        return $progressUserCourse;
    }

    private function getProgressAprovedPresentialAndVirtual()
    {
    }

    private function calculateAssistance(AnnouncementUser $announcementUser)
    {
        $announcementGroup = $announcementUser->getAnnouncementGroup();
        if (!$announcementGroup) {
            return 0;
        }

        $sessions = $announcementGroup->getAnnouncementGroupSessions();
        $totalAssistanceUser = 0;

        foreach ($sessions as $session) {
            if ($session->getStartAt() >= $session->getFinishAt()) {
                continue;
            }

            $assistance = $this->getAsistanceBySession($session);
            if (null === $assistance || [] === $assistance) {
                continue;
            }
            foreach ($assistance as $user) {
                if ($user['id'] === $announcementUser->getUser()->getId() && true === $user['assistance']) {
                    ++$totalAssistanceUser;
                    break;
                }
            }
        }

        return $totalAssistanceUser;
    }

    public function getProgressTotalAssistance(AnnouncementUser $announcementUser)
    {
        $totalAssistanceUser = $this->calculateAssistance($announcementUser);

        $announcementGroup = $announcementUser->getAnnouncementGroup();
        if (!$announcementGroup) {
            return 0;
        }

        $totalSessions = $announcementGroup->getNumSessions() ?? 0;

        if ($totalSessions > 0 && $totalAssistanceUser > 0) {
            return round(($totalAssistanceUser / $totalSessions) * 100);
        }

        return $totalAssistanceUser;
    }

    public function getTotalInPersonaAssistance(AnnouncementUser $announcementUser)
    {
        return $this->calculateAssistance($announcementUser);
    }

    // Esto va me permite aprobar un usuario si cumple los criterios aprobados
    public function verifyIfAprovedUserAnnouncement(AnnouncementUser $announcementUser)
    {
        $course = $announcementUser->getAnnouncement()->getCourse();

        if (TypeCourse::TYPE_PRESENCIAL == $course->getTypeCourse()->getId() || TypeCourse::TYPE_AULA_VIRTUAL == $course->getTypeCourse()->getId()) {
            return $this->verifyIfAprovedUserAnnouncementPresentialAndVirtual($announcementUser);
        }

        return $this->theUserMeetsTheRequirementsForApproval($announcementUser);
    }

    public function verifyIfAprovedUserAnnouncementPresentialAndVirtual(AnnouncementUser $announcementUser)
    {
        $percentageAprovedCriteriaUser = $this->getProgressTotalAssistance($announcementUser);

        return $percentageAprovedCriteriaUser >= $this->announcementAprovedCriteriaService->getPercentageForAprovedAnnoucement($announcementUser->getAnnouncement());
    }

    // Sacar si aprobo los capitulos
    public function verifyIfUserMinimumGradeToPassChapters(AnnouncementUser $announcementUser): bool
    {
        $userCourse = $this->getUserCourse($announcementUser);

        if (!$userCourse) {
            return false;
        }

        $progress = $this->getProgressUserCourse($announcementUser);
        $announcement = $announcementUser->getAnnouncement();

        $minGradeToPassChapters = $this->announcementAprovedCriteriaService->getValueMinimiunGradeToPassChapters($announcement);
        $hasMinGradeCriterion = $this->announcementAprovedCriteriaService->hasMinimumGradeToPassChapters($announcement);

        if ($hasMinGradeCriterion && $progress >= $minGradeToPassChapters) {
            return true;
        }

        if (!$hasMinGradeCriterion && $progress >= $this->announcementAprovedCriteriaService::PERCENTAGE_DEFAULT) {
            return true;
        }

        return false;
    }

    // Verificar si el usuario aprobo las tareas
    public function verifyIfUserCompleteTasks(AnnouncementUser $announcementUser)
    {
        $progress = $this->getProgressUserTask($announcementUser);
        $announcement = $announcementUser->getAnnouncement();

        $hasCompleteTasksCriterion = $this->announcementAprovedCriteriaService->hasCompleteTasks($announcement);
        $minTasksToPassCriterion = $this->announcementAprovedCriteriaService->getValueCompleteTasks($announcement);
        $hasTask = $this->hasAnnouncementTasks($announcementUser);

        if (!$hasCompleteTasksCriterion || !$hasTask) {
            return true;
        }

        if (($hasCompleteTasksCriterion && $hasTask) && $progress >= $minTasksToPassCriterion) {
            return true;
        }

        return false;
    }

    // Verificar si el usuario aprobo las horas
    public function verifyIfUserHoursOfCompletedTraining(AnnouncementUser $announcementUser)
    {
        $progress = $this->getProgressTotalHour($announcementUser);
        $announcement = $announcementUser->getAnnouncement();

        $hasHoursOfCompletedTrainingCriterion = $this->announcementAprovedCriteriaService->hasHoursOfCompletedTraining($announcement);
        $minHoursToPassCriterion = $this->announcementAprovedCriteriaService->getValueHoursOfCompletedTraining($announcement);

        if (!$hasHoursOfCompletedTrainingCriterion) {
            return true;
        }

        if ($hasHoursOfCompletedTrainingCriterion && $progress >= $minHoursToPassCriterion) {
            return true;
        }

        return false;
    }

    public function theUserMeetsTheRequirementsForApproval(AnnouncementUser $announcementUser)
    {
        $completedChapter = $this->verifyIfUserMinimumGradeToPassChapters($announcementUser);
        $completedTasks = $this->verifyIfUserCompleteTasks($announcementUser);
        $completedHours = $this->verifyIfUserHoursOfCompletedTraining($announcementUser);

        return $completedChapter && $completedTasks && $completedHours;
    }

    public function setAprovedAnnouncementUser(AnnouncementUser $announcementUser)
    {
        if (true === $announcementUser->isAproved()) {
            return;
        }

        if ($this->verifyIfAprovedUserAnnouncement($announcementUser)) {
            $announcementUser->setAproved(true);
            $announcementUser->setDateApproved(new \DateTimeImmutable('now'));
            $this->em->persist($announcementUser);
            $this->em->flush();
        }
    }

    public function getPercentageForAprovedAnnoucement(AnnouncementUser $announcementUser)
    {
        return $this->announcementAprovedCriteriaService->getPercentageForAprovedAnnoucement($announcementUser->getAnnouncement());
    }

    /**
     * Checks if a user has assistance in any session of the group.
     * If the user has no assistance in any session, removes approval.
     *
     * @param AnnouncementUser $announcementUser User to check
     * @param int|null         $excludeSessionId Session ID to exclude from verification (optional)
     *
     * @return bool True if the user has assistance in any session, false otherwise
     */
    public function removeApprovalIfNoAssistance(AnnouncementUser $announcementUser, ?int $excludeSessionId = null): bool
    {
        // If the user is not approved, nothing to do
        if (!$announcementUser->isAproved()) {
            return false;
        }

        $announcementGroup = $announcementUser->getAnnouncementGroup();
        if (!$announcementGroup) {
            return false;
        }

        $userId = $announcementUser->getUser()->getId();
        $hasAssistanceInAnySessions = false;

        // Get all sessions from the group
        $sessions = $announcementGroup->getAnnouncementGroupSessions();

        foreach ($sessions as $session) {
            // If there's a session to exclude and it's this one, skip it
            if (null !== $excludeSessionId && $session->getId() === $excludeSessionId) {
                continue;
            }

            // Check if the user has assistance in this session
            $assistance = $session->getAssistance() ?: [];

            foreach ($assistance as $item) {
                if (isset($item['id']) && $item['id'] === $userId && isset($item['assistance']) && true === $item['assistance']) {
                    $hasAssistanceInAnySessions = true;
                    break;
                }
            }

            if ($hasAssistanceInAnySessions) {
                break;
            }
        }

        // If the user has no assistance in any session, remove approval
        if (!$hasAssistanceInAnySessions) {
            $announcementUser->setAproved(false);
            $announcementUser->setDateApproved(null);
            $this->em->persist($announcementUser);
            $this->em->flush();
        }

        return $hasAssistanceInAnySessions;
    }

    /**
     * Updates the approval status of users based on assistance changes.
     *
     * @param array $previousAssistance Previous assistance
     * @param array $currentAssistance  Current assistance
     * @param array $announcementUsers  List of announcement users
     * @param int   $sessionId          ID of the session being modified
     */
    public function updateApprovalStatusBasedOnAssistanceChanges(
        array $previousAssistance,
        array $currentAssistance,
        array $announcementUsers,
        int $sessionId
    ): void {
        // Create maps to facilitate comparison
        $previousAssistanceMap = [];
        foreach ($previousAssistance as $item) {
            if (isset($item['id']) && isset($item['assistance'])) {
                $previousAssistanceMap[$item['id']] = $item['assistance'];
            }
        }

        $currentAssistanceMap = [];
        foreach ($currentAssistance as $item) {
            if (isset($item['id']) && isset($item['assistance'])) {
                $currentAssistanceMap[$item['id']] = $item['assistance'];
            }
        }

        foreach ($announcementUsers as $announcementUser) {
            $userId = $announcementUser->getUser()->getId();

            // If assistance has been removed for this user in this session
            if (
                isset($previousAssistanceMap[$userId])
                && true === $previousAssistanceMap[$userId]
                && isset($currentAssistanceMap[$userId])
                && false === $currentAssistanceMap[$userId]
            ) {
                // Check if the user has assistance in any other session and remove approval if necessary
                $this->removeApprovalIfNoAssistance($announcementUser, $sessionId);
            }

            // If assistance has been added, check if the user meets the criteria for approval
            if (
                (!isset($previousAssistanceMap[$userId]) || false === $previousAssistanceMap[$userId])
                && isset($currentAssistanceMap[$userId])
                && true === $currentAssistanceMap[$userId]
            ) {
                // Check if the user meets the approval criteria and update their status
                $this->setAprovedAnnouncementUser($announcementUser);
            }
        }
    }
}
