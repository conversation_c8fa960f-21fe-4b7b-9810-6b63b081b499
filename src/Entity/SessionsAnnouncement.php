<?php

namespace App\Entity;

use App\Repository\SessionsAnnouncementRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=SessionsAnnouncementRepository::class)
 */
class SessionsAnnouncement
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=Announcement::class, inversedBy="sessionsAnnouncement", cascade={"persist", "remove"})
     */
    private $announcement;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $hourSession;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $nameAnnouncement;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $classroom;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $numSessions;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $extra;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $assistance;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementGroup::class, mappedBy="sessionsAnnouncement", cascade={"persist", "remove"})
     */
    private $announcementGroup;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getHourSession(): ?int
    {
        return $this->hourSession;
    }

    public function setHourSession(?int $hourSession): self
    {
        $this->hourSession = $hourSession;

        return $this;
    }

    public function getNameAnnouncement(): ?string
    {
        return $this->nameAnnouncement;
    }

    public function setNameAnnouncement(?string $nameAnnouncement): self
    {
        $this->nameAnnouncement = $nameAnnouncement;

        return $this;
    }

    public function getClassroom(): ?string
    {
        return $this->classroom;
    }

    public function setClassroom(?string $classroom): self
    {
        $this->classroom = $classroom;

        return $this;
    }

    public function getNumSessions(): ?int
    {
        return $this->numSessions;
    }

    public function setNumSessions(?int $numSessions): self
    {
        $this->numSessions = $numSessions;

        return $this;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public function getAssistance(): ?string
    {
        return $this->assistance;
    }

    public function setAssistance(?string $assistance): self
    {
        $this->assistance = $assistance;

        return $this;
    }

    public function getAnnouncementGroup(): ?AnnouncementGroup
    {
        return $this->announcementGroup;
    }

    public function setAnnouncementGroup(?AnnouncementGroup $announcementGroup): self
    {
        // unset the owning side of the relation if necessary
        if ($announcementGroup === null && $this->announcementGroup !== null) {
            $this->announcementGroup->setSessionsAnnouncement(null);
        }

        // set the owning side of the relation if necessary
        if ($announcementGroup !== null && $announcementGroup->getSessionsAnnouncement() !== $this) {
            $announcementGroup->setSessionsAnnouncement($this);
        }

        $this->announcementGroup = $announcementGroup;

        return $this;
    }
}
