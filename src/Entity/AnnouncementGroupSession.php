<?php

namespace App\Entity;

use App\Repository\AnnouncementGroupSessionRepository;
use App\Utils\TimeZoneConverter\UtcTimezoneInterface;
use App\Utils\TimeZoneConverter\UtcTimezoneTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AnnouncementGroupSessionRepository::class)
* @Vich\Uploadable()
 */
class AnnouncementGroupSession implements UtcTimezoneInterface
{
    use UtcTimezoneTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"classroomvirtual"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementGroup::class, inversedBy="announcementGroupSessions")
     * @ORM\JoinColumn(nullable=false)
     * @Groups({"classroomvirtual"})
     */
    private $announcementGroup;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $startAt;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $finishAt;

   /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $url;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $assistance = [];


    /**
    * @ORM\Column(type="integer", nullable=true)
     * @Groups({"classroomvirtual"})
    */
   private $session_number;

   /**
    * @ORM\OneToOne(targetEntity=Classroomvirtual::class, mappedBy="groupSession", cascade={"persist", "remove"})
    */
   private $classroomvirtual;

   /**
    * @ORM\Column(type="json", nullable=true)
    */
   private $studentAssistance = [];

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementGroupSessionAssistanceFiles::class, mappedBy="announcementGroupSession", orphanRemoval=true)
     */
    private $assistanceFiles;

    /**
     * Min = 0; Max = 100;
     * Value considered as percentage
     * @ORM\Column(type="integer", nullable=true)
     */
    private $entryMargin;

    /**
     * Min = 0; Max = 100
     * Value considered as percentage
     * @ORM\Column(type="integer", nullable=true)
     */
    private $exitMargin;

    /**
     * @ORM\Column(type="string", length=150, nullable=true)
     */
    private $timezone;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $place;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementUserDigitalSignature::class, mappedBy="announcementGroupSession")
     */
    private $announcementUserDigitalSignatures;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $cost;

    /**
     * @ORM\ManyToOne(targetEntity=TypeMoney::class, inversedBy="announcementGroupSessions")
     */
    private $typeMoney;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $type;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementModality::class, inversedBy="announcementGroupSessions")
     */
    private $modality;

    public function __construct()
    {
        $this->assistanceFiles = new ArrayCollection();
        $this->announcementUserDigitalSignatures = new ArrayCollection();
    }



    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAnnouncementGroup(): ?AnnouncementGroup
    {
        return $this->announcementGroup;
    }

    public function setAnnouncementGroup(?AnnouncementGroup $announcementGroup): self
    {
        $this->announcementGroup = $announcementGroup;

        return $this;
    }

    /**
     * @return \DateTimeImmutable|null In UTC timezone
     */
    public function getStartAt(): ?\DateTimeImmutable
    {
        return $this->startAt;
    }

    public function setStartAt(\DateTimeImmutable $startAt): self
    {
        $this->startAt = $startAt;

        return $this;
    }

    /**
     * @return \DateTimeImmutable|null UTC Timezone
     */
    public function getFinishAt(): ?\DateTimeImmutable
    {
        return $this->finishAt;
    }

    public function setFinishAt(\DateTimeImmutable $finishAt): self
    {
        $this->finishAt = $finishAt;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getAssistance(): ?array
    {
        return $this->assistance;
    }

    public function setAssistance(?array $assistance): self
    {
        $this->assistance = $assistance;

        return $this;
    }

    public function getSessionNumber(): ?int
    {
        return $this->session_number;
    }

    public function setSessionNumber(?int $session_number): self
    {
        $this->session_number = $session_number;

        return $this;
    }

    public function getClassroomvirtual(): ?Classroomvirtual
    {
        return $this->classroomvirtual;
    }

    public function setClassroomvirtual(?Classroomvirtual $classroomvirtual): self
    {
        // unset the owning side of the relation if necessary
        if ($classroomvirtual === null && $this->classroomvirtual !== null) {
            $this->classroomvirtual->setGroupSession(null);
        }

        // set the owning side of the relation if necessary
        if ($classroomvirtual !== null && $classroomvirtual->getGroupSession() !== $this) {
            $classroomvirtual->setGroupSession($this);
        }

        $this->classroomvirtual = $classroomvirtual;

        return $this;
    }

    public function getStudentAssistance(): ?array
    {
        return $this->studentAssistance;
    }

    public function setStudentAssistance(?array $studentAssistance): self
    {
        $this->studentAssistance = $studentAssistance;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementGroupSessionAssistanceFiles>
     */
    public function getAssistanceFiles(): Collection
    {
        return $this->assistanceFiles;
    }

    public function addAssistanceFile(AnnouncementGroupSessionAssistanceFiles $assistanceFile): self
    {
        if (!$this->assistanceFiles->contains($assistanceFile)) {
            $this->assistanceFiles[] = $assistanceFile;
            $assistanceFile->setAnnouncementGroupSession($this);
        }

        return $this;
    }

    public function removeAssistanceFile(AnnouncementGroupSessionAssistanceFiles $assistanceFile): self
    {
        if ($this->assistanceFiles->removeElement($assistanceFile)) {
            // set the owning side to null (unless already changed)
            if ($assistanceFile->getAnnouncementGroupSession() === $this) {
                $assistanceFile->setAnnouncementGroupSession(null);
            }
        }

        return $this;
    }

    public function getEntryMargin(): ?int
    {
        return $this->entryMargin ?? 0;
    }

    /**
     * @param int|null $entryMargin Value between 0-100
     * @return $this
     */
    public function setEntryMargin(?int $entryMargin): self
    {
        $value = $entryMargin > 100 ? 100 : $entryMargin;
        $value = $value < 0 ? 0 : $value;
        $this->entryMargin = $value;

        return $this;
    }

    public function getExitMargin(): ?int
    {
        return $this->exitMargin ?? 0;
    }

    /**
     * @param int|null $exitMargin Value between 0-100
     * @return $this
     */
    public function setExitMargin(?int $exitMargin): self
    {
        $value = $exitMargin > 100 ? 100 : $exitMargin;
        $value = $value < 0 ? 0 : $value;
        $this->exitMargin = $value;

        return $this;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getSessionTimeInMinutes(): int {
        $diffResult = $this->getFinishAt()->diff($this->getStartAt());
        $sessionTimeInMinutes = $diffResult->days * 24 * 60;
        $sessionTimeInMinutes += $diffResult->h * 60;
        $sessionTimeInMinutes += $diffResult->i;

        return $sessionTimeInMinutes;
    }

    public function getEntryMarginInMinutes() {
        return floor($this->getSessionTimeInMinutes() * $this->getEntryMargin() / 100);
    }

    public function getExitMarginInMinutes() {
        return floor($this->getSessionTimeInMinutes() * $this->getExitMargin() / 100);
    }

    public function isEntryUserInTime(\DateTimeImmutable $userTime): bool {
        $userDateTime = $userTime->setTimezone(new \DateTimeZone('UTC'));// Convert datetime to UTC to avoid miscalculation
        $minutes = $this->getEntryMarginInMinutes();
        $maxTime = $this->getStartAt()->modify("+$minutes minutes");
        return $userDateTime <= $maxTime;
    }

    public function isExitUserInTime(\DateTimeImmutable $userTime): bool {
        $userDateTime = $userTime->setTimezone(new \DateTimeZone('UTC'));// Convert datetime to UTC to avoid miscalculation
        $minutes = $this->getExitMarginInMinutes();
        $minTime = $this->getFinishAt()->modify("-$minutes minutes");
        $maxTime = $this->getFinishAt()->modify("+$minutes minutes");

        return $minTime <= $userDateTime && $userDateTime <= $maxTime;
    }

    public function getPlace(): ?string
    {
        return $this->place;
    }

    public function setPlace(?string $place): self
    {
        $this->place = $place;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementUserDigitalSignature>
     */
    public function getAnnouncementUserDigitalSignatures(): Collection
    {
        return $this->announcementUserDigitalSignatures;
    }

    public function addAnnouncementUserDigitalSignature(AnnouncementUserDigitalSignature $announcementUserDigitalSignature): self
    {
        if (!$this->announcementUserDigitalSignatures->contains($announcementUserDigitalSignature)) {
            $this->announcementUserDigitalSignatures[] = $announcementUserDigitalSignature;
            $announcementUserDigitalSignature->setAnnouncementGroupSession($this);
        }

        return $this;
    }

    public function removeAnnouncementUserDigitalSignature(AnnouncementUserDigitalSignature $announcementUserDigitalSignature): self
    {
        if ($this->announcementUserDigitalSignatures->removeElement($announcementUserDigitalSignature)) {
            // set the owning side to null (unless already changed)
            if ($announcementUserDigitalSignature->getAnnouncementGroupSession() === $this) {
                $announcementUserDigitalSignature->setAnnouncementGroupSession(null);
            }
        }

        return $this;
    }

    public function getStatus(): string
    {
        $tz = $this->getStartAt()->getTimezone();
        $current = new \DateTimeImmutable('now', $tz);
        if ($current < $this->getStartAt()) return 'PENDING';
        if ($current > $this->getFinishAt()) return 'FINISHED';
        return 'IN_PROGRESS';
    }

    public function getCost(): ?string
    {
        return $this->cost;
    }

    public function setCost(?string $cost): self
    {
        $this->cost = $cost;

        return $this;
    }

    public function getTypeMoney(): ?TypeMoney
    {
        return $this->typeMoney;
    }

    public function setTypeMoney(?TypeMoney $typeMoney): self
    {
        $this->typeMoney = $typeMoney;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function __clone()
    {
        $this->id = null;
        $this->assistanceFiles = new ArrayCollection();
        $this->announcementUserDigitalSignatures = new ArrayCollection();
        $this->classroomvirtual = null;
        $this->studentAssistance = [];
        $this->url = null;
    }

    public function getModality(): ?AnnouncementModality
    {
        return $this->modality;
    }

    public function setModality(?AnnouncementModality $modality): self
    {
        $this->modality = $modality;

        return $this;
    }
}
