<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=UserRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @UniqueEntity(fields="email", errorPath="email", message="Duplicate Email")
 *
 * @Vich\Uploadable()
 */
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    use AtAndBy;

    public const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';
    public const ROLE_ADMIN = 'ROLE_ADMIN';
    public const ROLE_USER = 'ROLE_USER';
    public const ROLE_TUTOR = 'ROLE_TUTOR';
    public const ROLE_SUBSIDIZER = 'ROLE_SUBSIDIZER';
    public const ROLE_MANAGER = 'ROLE_MANAGER';
    public const ROLE_MANAGER_EDITOR = 'ROLE_MANAGER_EDITOR';
    public const ROLE_TEAM_MANAGER = 'ROLE_TEAM_MANAGER';
    public const ROLE_INSPECTOR = 'ROLE_INSPECTOR';
    public const ROLE_DEVELOPER = 'ROLE_DEVELOPER';
    public const ROLE_CREATOR = 'ROLE_CREATOR';

    public const string ROLE_SUPPORT = 'ROLE_SUPPORT';

    public const IDENTIFIER_EMAIL = 'email';
    public const IDENTIFIER_CODE = 'code';

    public const ROLES = [
        self::ROLE_SUPER_ADMIN => 'user.roles.super_administrator',
        self::ROLE_ADMIN => 'user.roles.administrator',
        self::ROLE_USER => 'user.roles.user',
        self::ROLE_TUTOR => 'user.roles.tutor',
        self::ROLE_SUBSIDIZER => 'user.roles.subsidizer',
        self::ROLE_MANAGER => 'user.roles.manager',
        self::ROLE_MANAGER_EDITOR => 'user.roles.manager_editor',
        self::ROLE_TEAM_MANAGER => 'user.roles.team_manager',
        self::ROLE_INSPECTOR => 'user.roles.inspector',
        self::ROLE_CREATOR => 'user.roles.creator',
        self::ROLE_SUPPORT => 'user.roles.support',
    ];

    public const ALL_USERS = 'ALL_USERS';
    public const ACTIVE_USERS = 'ACTIVE_USERS';
    public const INACTIVE_USERS = 'INACTIVE_USERS';
    public const ACTIVITY_STATUS_FILTER = [
        self::ALL_USERS => 2,
        self::ACTIVE_USERS => 1,
        self::INACTIVE_USERS => 0
    ];

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({
     *     "user_area", "announcement", "messages", "emailing",
     *     "ranking", "itinerary", "sharefile","challenge_users",
     *     "challenge_user_points","challenge_userscalled","challenge_ranking",
     *     "challenge_user_statistics","challenge_duel_details","forum",
     *     "user:admin:list", "progress"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=180, unique=true)
     *
     * @Groups({
     *     "user_area", "announcement", "messages","ranking",
     *     "itinerary","sharefile","challenge_ranking","challenge_userscalled",
     *     "user:admin:list"})
     */
    private $email;

    /**
     * @ORM\Column(type="json")
     */
    private $roles = [];

    /**
     * @ORM\Column(type="json")
     */
    private $remoteRoles = [];

    /**
     * @var string The hashed password
     *
     * @ORM\Column(type="string")
     */
    private $password;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"user_area", "announcement", "messages", "progress","ranking", "itinerary", "challenge_users","challenge_ranking","challenge_userscalled","challenge_user_statistics","challenge_duel_details","forum","messages"})
     */
    private $firstName;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"user_area", "announcement", "messages","ranking", "itinerary", "challenge_users","challenge_ranking","challenge_userscalled","challenge_user_statistics","challenge_duel_details","forum","messages"})
     */
    private $lastName;

    /**
     * @ORM\OneToOne(targetEntity=UserExtra::class, mappedBy="user", cascade={"persist", "remove"})
     *
     * @Groups({"user_area", "announcement"})
     */
    private $extra;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementUser::class, mappedBy="user", cascade={"persist", "remove"})
     */
    private $announcements;

    /**
     * @ORM\OneToMany(targetEntity=UserCourse::class, mappedBy="user", cascade={"persist", "remove"})
     */
    private $courses;

    /**
     * @ORM\Column(name="is_active", type="boolean")
     */
    private $isActive;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $code;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"user_area","ranking"})
     */
    private $points;

    /**
     * @ORM\OneToMany(targetEntity=UserComments::class, mappedBy="user")
     */
    private $userComments;

    /**
     * @ORM\OneToMany(targetEntity=Message::class, mappedBy="sender", orphanRemoval=true)
     */
    private $messagesSent;

    /**
     * @ORM\OneToMany(targetEntity=Message::class, mappedBy="recipient", orphanRemoval=true)
     */
    private $messagesReceived;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"user_area","ranking","forum","challenge_user_statistics","duel_detail","challenge_ranking"})
     */
    private $avatar;

    /**
     * @Vich\UploadableField(mapping="avatar_user", fileNameProperty="avatar")
     */
    private $avatarFile;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     *
     * @Groups({"user_area"})
     *
     * @Assert\NotBlank
     */
    private $locale;
    /**
     * @ORM\OneToMany(targetEntity=Notification::class, mappedBy="user", orphanRemoval=true)
     */
    private $notifications;

    /**
     * @ORM\Column(type="string", length=400, nullable=true)
     *
     * @Groups({"user_area"})
     */
    private $dataAvatar;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $registerKey;

    /**
     * @ORM\OneToMany(targetEntity=UserLogin::class, mappedBy="user", orphanRemoval=true)
     */
    private $userLogins;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementTutor::class, mappedBy="tutor", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $tutorships;

    /**
     * @ORM\OneToMany(targetEntity=ForumPost::class, mappedBy="user")
     */
    private $forumPosts;

    /**
     * @ORM\OneToMany(targetEntity=Nps::class, mappedBy="user")
     */
    private $nps;

    /**
     * @ORM\ManyToMany(targetEntity=Course::class, mappedBy="managers")
     */
    private $managedCourses;

    /**
     * @ORM\Column(type="boolean")
     */
    private $validated;

    /**
     * @ORM\OneToMany(targetEntity=EmailRecipient::class, mappedBy="user")
     */
    private $emailsReceived;

    /**
     * @ORM\OneToOne(targetEntity=UserManage::class, mappedBy="user", cascade={"persist", "remove"})
     */
    private $manage;

    /**
     * @ORM\OneToMany(targetEntity=RecoveryCode::class, mappedBy="user", cascade={"remove"})
     */
    private $recoveryCodes;

    /**
     * @ORM\Column(type="boolean")
     */
    private $open;

    /**
     * @ORM\ManyToMany(targetEntity=Filter::class, inversedBy="users")
     *
     * @Groups({"ranking"})
     */
    private $filter;

    /**
     * @ORM\ManyToMany(targetEntity=Filter::class, inversedBy="managerFilters")
     *
     * @ORM\JoinTable(name="manager_filter",
     *      joinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="filter_id", referencedColumnName="id")}
     *      )
     */
    private $filters;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $meta = [];

    /**
     * @ORM\OneToMany(targetEntity=ItineraryUser::class, mappedBy="user")
     */
    private $itineraryUsers;

    /**
     * @ORM\OneToMany(targetEntity=EmailNotification::class, mappedBy="user")
     */
    private $emailNotificationUser;

    /**
     * @ORM\OneToMany(targetEntity=ForumLikes::class, mappedBy="user")
     */
    private $forumLikes;

    /**
     * @ORM\OneToMany(targetEntity=ForumReport::class, mappedBy="user")
     */
    private $forumReports;

    /**
     * @ORM\OneToMany(targetEntity=UserVcmsProject::class, mappedBy="user", orphanRemoval=true)
     */
    private $vcmsProjects;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeUser::class, mappedBy="user")
     */
    private $challengeUsers;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeUserPoints::class, mappedBy="user")
     */
    private $challengeUserPoints;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeDuel::class, mappedBy="user1")
     */
    private $challengeDuels;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeDuelQuestionsAdn::class, mappedBy="user")
     */
    private $challengeDuelQuestionsAdns;

    /**
     * @ORM\OneToMany(targetEntity=UserNotification::class, mappedBy="user")
     */
    private $userNotification;

    private ?string $newPassword = null;

    /**
     * @ORM\OneToMany(targetEntity=ItineraryManager::class, mappedBy="user")
     */
    private $itineraryManagers;

    /**
     * @ORM\ManyToMany(targetEntity=Itinerary::class, mappedBy="managers")
     */
    private $managerItineraries;

    /**
     * Used for all user filtrs in vueapp.
     */
    private $filtersJson;

    /**
     * @ORM\OneToMany(targetEntity=UserToken::class, mappedBy="user", orphanRemoval=true)
     */
    private $userTokens;

    /**
     * @ORM\OneToMany(targetEntity=MaterialDownloadHistory::class, mappedBy="user")
     */
    private $materialDownloadHistories;

    /**
     * @ORM\OneToMany(targetEntity=HistorySeenMaterial::class, mappedBy="user")
     */
    private $historySeenMaterials;

    /**
     * @ORM\OneToMany(targetEntity=TaskUser::class, mappedBy="user")
     */
    private $taskUsers;

    /**
     * @ORM\OneToMany(targetEntity=LibraryViews::class, mappedBy="user", orphanRemoval=true)
     */
    private $libraryViews;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="teamUsers")
     */
    private $teamManager;

    /**
     * @ORM\OneToMany(targetEntity=User::class, mappedBy="teamManager")
     */
    private $teamUsers;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $teamManagerEmail;

    /**
     * @ORM\OneToMany(targetEntity=UserTime::class, mappedBy="user", orphanRemoval=true)
     */
    private $userTimes;

    /**
     * @ORM\OneToOne(targetEntity=UserCoursesTotalTime::class, mappedBy="user", cascade={"persist", "remove"})
     */
    private $userCoursesTotalTime;

    /**
     * @ORM\Column(type="boolean")
     */
    private $starteam = false;

    /**
     *  @ORM\OneToMany(targetEntity=UserRoleplayProject::class, mappedBy="user", orphanRemoval=true)
     */
    private $roleplayProjects;

    /**
     * Manually assigned filters on platform.
     *
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $customFilters = [];

    /**
     * @ORM\OneToMany(targetEntity=ChatMessage::class, mappedBy="user", orphanRemoval=true)
     */
    private $chatMessages;

    /**
     * @ORM\OneToMany(targetEntity=ChatChannelUser::class, mappedBy="user", orphanRemoval=true)
     */
    private $chatChannelUsers;

    /**
     * @ORM\OneToMany(targetEntity=ChatMessageLike::class, mappedBy="user", orphanRemoval=true)
     */
    private $chatMessageLikes;

    /**
     * @ORM\OneToMany(targetEntity=ChatMessageReport::class, mappedBy="user", orphanRemoval=true)
     */
    private $chatMessageReports;

    /**
     * @ORM\OneToMany(targetEntity=GenericToken::class, mappedBy="createdBy", orphanRemoval=true)
     */
    private $genericTokens;

    /**
     * @ORM\OneToOne(targetEntity=UserFieldsFundae::class, mappedBy="user", cascade={"persist", "remove"})
     */
    private $userFieldsFundae;

    /**
     * @ORM\Column(type="string", length=150, nullable=true)
     */
    private $timezone;

    /**
     * @ORM\OneToMany(targetEntity=UserIdentification::class, mappedBy="user", orphanRemoval=true)
     */
    private $userIdentifications;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $localeCampus;

    public function __construct()
    {
        $this->updateBy = new ArrayCollection();
        $this->announcements = new ArrayCollection();
        $this->courses = new ArrayCollection();
        $this->userComments = new ArrayCollection();
        $this->isActive = false;
        $this->messagesSent = new ArrayCollection();
        $this->messagesReceived = new ArrayCollection();
        $this->notifications = new ArrayCollection();
        $this->userLogins = new ArrayCollection();
        $this->forumPosts = new ArrayCollection();
        $this->tutorships = new ArrayCollection();
        $this->nps = new ArrayCollection();
        $this->managedCourses = new ArrayCollection();
        $this->validated = false;
        $this->emailsReceived = new ArrayCollection();
        $this->challengeUsers = new ArrayCollection();
        $this->challengeUserPoints = new ArrayCollection();
        $this->challengeDuels = new ArrayCollection();
        $this->challengeDuelQuestionsAdns = new ArrayCollection();
        $this->filter = new ArrayCollection();
        $this->filters = new ArrayCollection();
        $this->vcmsProjects = new ArrayCollection();
        $this->itineraryUsers = new ArrayCollection();
        $this->itineraryManagers = new ArrayCollection();
        $this->managerItineraries = new ArrayCollection();
        $this->userTokens = new ArrayCollection();
        $this->forumLikes = new ArrayCollection();
        $this->forumReports = new ArrayCollection();
        $this->materialDownloadHistories = new ArrayCollection();
        $this->historySeenMaterials = new ArrayCollection();
        $this->taskUsers = new ArrayCollection();
        $this->libraryViews = new ArrayCollection();
        $this->teamUsers = new ArrayCollection();
        $this->userTimes = new ArrayCollection();
        $this->chatMessages = new ArrayCollection();
        $this->chatChannelUsers = new ArrayCollection();
        $this->chatMessageLikes = new ArrayCollection();
        $this->chatMessageReports = new ArrayCollection();
        $this->roleplayProjects = new ArrayCollection();
        $this->genericTokens = new ArrayCollection();
        $this->userIdentifications = new ArrayCollection();
    }

    public function __toString(): string
    {
        return (string) $this->email;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUsername(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';
        $roles = array_merge($roles, $this->getRemoteRoles());

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = array_values($roles);

        return $this;
    }

    public function getRemoteRoles(): array
    {
        return $this->remoteRoles ?? [];
    }

    public function setRemoteRoles(array $remoteRoles): self
    {
        $this->remoteRoles = $remoteRoles;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getPassword(): string
    {
        return (string) $this->password;
    }

    /**
     * @param string $password Plain password  when is new User otherwise pass hashed password to update the User entity
     *
     * @return $this
     */
    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getSalt()
    {
        // not needed when using the "bcrypt" algorithm in security.yaml
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials()
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function newPassword()
    {
        return null;
    }

    public function getNewPassword(): ?string
    {
        return $this->newPassword;
    }

    public function setNewPassword($password)
    {
        $this->newPassword = $password;
    }

    public function hashPassword(UserPasswordHasherInterface $passwordEncoder)
    {
        if (!\is_null($this->password)) {
            $this->password = $passwordEncoder->hashPassword($this, $this->password);
        }
    }

    public function getExtra(): ?UserExtra
    {
        return $this->extra;
    }

    public function setExtra(UserExtra $extra): self
    {
        $this->extra = $extra;

        // set the owning side of the relation if necessary
        if ($extra->getUser() !== $this) {
            $extra->setUser($this);
        }

        return $this;
    }

    /**
     * @return Collection|Announcement[]
     */
    public function getAnnouncements(): Collection
    {
        return $this->announcements;
    }

    public function addAnnouncement(Announcement $announcement): self
    {
        if (!$this->announcements->contains($announcement)) {
            $this->announcements[] = $announcement;
            $announcement->addCalled($this);
        }

        return $this;
    }

    public function removeAnnouncement(Announcement $announcement): self
    {
        if ($this->announcements->contains($announcement)) {
            $this->announcements->removeElement($announcement);
            $announcement->removeCalled($this);
        }

        return $this;
    }

    /**
     * @return Collection|UserCourse[]
     */
    public function getCourses(): Collection
    {
        return $this->courses;
    }

    public function addCourse(UserCourse $course): self
    {
        if (!$this->courses->contains($course)) {
            $this->courses[] = $course;
            $course->setUser($this);
        }

        return $this;
    }

    public function removeCourse(UserCourse $course): self
    {
        if ($this->courses->contains($course)) {
            $this->courses->removeElement($course);
            // set the owning side to null (unless already changed)
            if ($course->getUser() === $this) {
                $course->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|UserComments[]
     */
    public function getUserComments(): Collection
    {
        return $this->userComments;
    }

    public function addUserComment(UserComments $userComment): self
    {
        if (!$this->userComments->contains($userComment)) {
            $this->userComments[] = $userComment;
            $userComment->setUser($this);
        }

        return $this;
    }

    public function removeUserComment(UserComments $userComment): self
    {
        if ($this->userComments->contains($userComment)) {
            $this->userComments->removeElement($userComment);
            // set the owning side to null (unless already changed)
            if ($userComment->getUser() === $this) {
                $userComment->setUser(null);
            }
        }

        return $this;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function setPoints(?int $points): self
    {
        $this->points = $points;

        return $this;
    }

    /**
     * @return Collection|ChallengeUser[]
     */
    public function getChallengeUsers(): Collection
    {
        return $this->challengeUsers;
    }

    public function addChallengeUser(ChallengeUser $challengeUser): self
    {
        if (!$this->challengeUsers->contains($challengeUser)) {
            $this->challengeUsers[] = $challengeUser;
            $challengeUser->setUser($this);
        }

        return $this;
    }

    public function removeChallengeUser(ChallengeUser $challengeUser): self
    {
        if ($this->challengeUsers->removeElement($challengeUser)) {
            // set the owning side to null (unless already changed)
            if ($challengeUser->getUser() === $this) {
                $challengeUser->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ChallengeUserPoints[]
     */
    public function getChallengeUserPoints(): Collection
    {
        return $this->challengeUserPoints;
    }

    public function addChallengeUserPoint(ChallengeUserPoints $challengeUserPoint): self
    {
        if (!$this->challengeUserPoints->contains($challengeUserPoint)) {
            $this->challengeUserPoints[] = $challengeUserPoint;
            $challengeUserPoint->setUser($this);
        }

        return $this;
    }

    public function removeChallengeUserPoint(ChallengeUserPoints $challengeUserPoint): self
    {
        if ($this->challengeUserPoints->removeElement($challengeUserPoint)) {
            // set the owning side to null (unless already changed)
            if ($challengeUserPoint->getUser() === $this) {
                $challengeUserPoint->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ChallengeDuel[]
     */
    public function getChallengeDuels(): Collection
    {
        return $this->challengeDuels;
    }

    public function addChallengeDuel(ChallengeDuel $challengeDuel): self
    {
        if (!$this->challengeDuels->contains($challengeDuel)) {
            $this->challengeDuels[] = $challengeDuel;
            $challengeDuel->setUser1($this);
        }

        return $this;
    }

    public function removeChallengeDuel(ChallengeDuel $challengeDuel): self
    {
        if ($this->challengeDuels->removeElement($challengeDuel)) {
            // set the owning side to null (unless already changed)
            if ($challengeDuel->getUser1() === $this) {
                $challengeDuel->setUser1(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Message[]
     */
    public function getMessagesSent(): Collection
    {
        return $this->messagesSent;
    }

    public function addMessagesSent(Message $messagesSent): self
    {
        if (!$this->messagesSent->contains($messagesSent)) {
            $this->messagesSent[] = $messagesSent;
            $messagesSent->setSender($this);
        }

        return $this;
    }

    public function removeMessagesSent(Message $messagesSent): self
    {
        if ($this->messagesSent->removeElement($messagesSent)) {
            // set the owning side to null (unless already changed)
            if ($messagesSent->getSender() === $this) {
                $messagesSent->setSender(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Message[]
     */
    public function getMessagesReceived(): Collection
    {
        return $this->messagesReceived;
    }

    public function addMessagesReceived(Message $messagesReceived): self
    {
        if (!$this->messagesReceived->contains($messagesReceived)) {
            $this->messagesReceived[] = $messagesReceived;
            $messagesReceived->setRecipient($this);
        }

        return $this;
    }

    public function removeMessagesReceived(Message $messagesReceived): self
    {
        if ($this->messagesReceived->removeElement($messagesReceived)) {
            // set the owning side to null (unless already changed)
            if ($messagesReceived->getRecipient() === $this) {
                $messagesReceived->setRecipient(null);
            }
        }

        return $this;
    }

    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    public function setAvatar(?string $avatar): self
    {
        $this->avatar = $avatar;

        return $this;
    }

    public function getAvatarFile()
    {
        return $this->avatarFile;
    }

    public function setAvatarFile($avatarFile): self
    {
        $this->avatarFile = $avatarFile;

        if ($avatarFile) {
            $this->updatedAt = new \DateTime();
        }

        return $this;
    }

    public function getAvatarImage()
    {
        return $this->avatar ? $this->avatar : 'default.svg';
    }

    public function getLocale(): ?string
    {
        return $this->locale ?? 'es';
    }

    public function setLocale(?string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @return Collection|Notification[]
     */
    public function getNotifications(): Collection
    {
        return $this->notifications;
    }

    public function addNotification(Notification $notification): self
    {
        if (!$this->notifications->contains($notification)) {
            $this->notifications[] = $notification;
            $notification->setUser($this);
        }

        return $this;
    }

    public function removeNotification(Notification $notification): self
    {
        if ($this->notifications->removeElement($notification)) {
            // set the owning side to null (unless already changed)
            if ($notification->getUser() === $this) {
                $notification->setUser(null);
            }
        }

        return $this;
    }

    public function getDataAvatar(): ?string
    {
        return $this->dataAvatar;
    }

    public function setDataAvatar(?string $dataAvatar): self
    {
        $this->dataAvatar = $dataAvatar;

        return $this;
    }

    public function getRegisterKey(): ?string
    {
        return $this->registerKey;
    }

    public function setRegisterKey(?string $registerKey): self
    {
        $this->registerKey = $registerKey;

        return $this;
    }

    /**
     * @Groups({"emailing", "user_area", "user:admin:list"})
     *
     * @return string
     */
    public function getFullName()
    {
        return $this->getFirstName() . ' ' . $this->getLastName();
    }

    public function getFullNameReversed()
    {
        return $this->getLastName() . ', ' . $this->getFirstName();
    }

    /**
     * @return Collection|UserLogin[]
     */
    public function getUserLogins(): Collection
    {
        return $this->userLogins;
    }

    public function addUserLogin(UserLogin $userLogin): self
    {
        if (!$this->userLogins->contains($userLogin)) {
            $this->userLogins[] = $userLogin;
            $userLogin->setUser($this);
        }

        return $this;
    }

    public function removeUserLogin(UserLogin $userLogin): self
    {
        if ($this->userLogins->removeElement($userLogin)) {
            // set the owning side to null (unless already changed)
            if ($userLogin->getUser() === $this) {
                $userLogin->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|AnnouncementTutor[]
     */
    public function getTutorships(): Collection
    {
        return $this->tutorships;
    }

    public function addTutorship(AnnouncementTutor $tutorship): self
    {
        if (!$this->tutorships->contains($tutorship)) {
            $this->tutorships[] = $tutorship;
            $tutorship->setTutor($this);
        }

        return $this;
    }

    public function removeTutorship(AnnouncementTutor $tutorship): self
    {
        if ($this->tutorships->removeElement($tutorship)) {
            // set the owning side to null (unless already changed)
            if ($tutorship->getTutor() === $this) {
                $tutorship->setTutor(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ForumPost[]
     */
    public function getForumPosts(): Collection
    {
        return $this->forumPosts;
    }

    public function addForumPost(ForumPost $forumPost): self
    {
        if (!$this->forumPosts->contains($forumPost)) {
            $this->forumPosts[] = $forumPost;
            $forumPost->setUser($this);
        }

        return $this;
    }

    public function removeForumPost(ForumPost $forumPost): self
    {
        if ($this->forumPosts->contains($forumPost)) {
            $this->forumPosts->removeElement($forumPost);
            // set the owning side to null (unless already changed)
            if ($forumPost->getUser() === $this) {
                $forumPost->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Nps[]
     */
    public function getNps(): Collection
    {
        return $this->nps;
    }

    public function addNp(Nps $np): self
    {
        if (!$this->nps->contains($np)) {
            $this->nps[] = $np;
            $np->setUser($this);
        }

        return $this;
    }

    public function removeNp(Nps $np): self
    {
        if ($this->nps->removeElement($np)) {
            // set the owning side to null (unless already changed)
            if ($np->getUser() === $this) {
                $np->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Course[]
     */
    public function getManagedCourses(): Collection
    {
        return $this->managedCourses;
    }

    public function addManagedCourse(Course $managedCourse): self
    {
        if (!$this->managedCourses->contains($managedCourse)) {
            $this->managedCourses[] = $managedCourse;
            $managedCourse->addManager($this);
        }

        return $this;
    }

    public function removeManagedCourse(Course $managedCourse): self
    {
        if ($this->managedCourses->removeElement($managedCourse)) {
            $managedCourse->removeManager($this);
        }

        return $this;
    }

    public function getValidated(): ?bool
    {
        return $this->validated;
    }

    public function setValidated($validated): self
    {
        if (!\is_int($validated) && !\is_bool($validated)) {
            throw new \InvalidArgumentException('the $validated should by int or bool.');
        } elseif (\is_int($validated)) {
            $validated = (bool) $validated;
        }

        $this->validated = $validated;

        return $this;
    }

    /**
     * @return Collection|ChallengeDuelQuestionsAdn[]
     */
    public function getChallengeDuelQuestionsAdns(): Collection
    {
        return $this->challengeDuelQuestionsAdns;
    }

    public function addChallengeDuelQuestionsAdn(ChallengeDuelQuestionsAdn $challengeDuelQuestionsAdn): self
    {
        if (!$this->challengeDuelQuestionsAdns->contains($challengeDuelQuestionsAdn)) {
            $this->challengeDuelQuestionsAdns[] = $challengeDuelQuestionsAdn;
            $challengeDuelQuestionsAdn->setUser($this);
        }

        return $this;
    }

    public function removeChallengeDuelQuestionsAdn(ChallengeDuelQuestionsAdn $challengeDuelQuestionsAdn): self
    {
        if ($this->challengeDuelQuestionsAdns->removeElement($challengeDuelQuestionsAdn)) {
            // set the owning side to null (unless already changed)
            if ($challengeDuelQuestionsAdn->getUser() === $this) {
                $challengeDuelQuestionsAdn->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|EmailRecipient[]
     */
    public function getEmailsReceived(): Collection
    {
        return $this->emailsReceived;
    }

    public function addEmailsReceived(EmailRecipient $emailsReceived): self
    {
        if (!$this->emailsReceived->contains($emailsReceived)) {
            $this->emailsReceived[] = $emailsReceived;
            $emailsReceived->setUser($this);
        }

        return $this;
    }

    public function removeEmailsReceived(EmailRecipient $emailsReceived): self
    {
        if ($this->emailsReceived->removeElement($emailsReceived)) {
            // set the owning side to null (unless already changed)
            if ($emailsReceived->getUser() === $this) {
                $emailsReceived->setUser(null);
            }
        }

        return $this;
    }

    public function getManage(): ?UserManage
    {
        return $this->manage;
    }

    public function setManage(UserManage $manage): self
    {
        // set the owning side of the relation if necessary
        if ($manage->getUser() !== $this) {
            $manage->setUser($this);
        }

        $this->manage = $manage;

        return $this;
    }

    public function addRole(string $role): self
    {
        if (\in_array($role, array_keys(self::ROLES))) {
            $this->roles[] = $role;
        }

        $this->roles = array_unique($this->roles);

        return $this;
    }

    public function hasRole($role): bool
    {
        return \in_array($role, $this->getRoles());
    }

    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN) || $this->isSuperAdmin();
    }

    public function isSuperAdmin(): bool
    {
        return $this->hasRole(self::ROLE_SUPER_ADMIN);
    }

    public function isManager(): bool
    {
        return $this->hasRole(self::ROLE_MANAGER);
    }

    public function isManagerEditor(): bool
    {
        return $this->hasRole(self::ROLE_MANAGER_EDITOR);
    }

    public function isTeamManager(): bool
    {
        return $this->hasRole(self::ROLE_TEAM_MANAGER);
    }

    public function isTutor(): bool
    {
        return $this->hasRole(self::ROLE_TUTOR);
    }

    public function isSubsidizer(): bool
    {
        return $this->hasRole(self::ROLE_SUBSIDIZER);
    }

    public function isInspector(): bool
    {
        return $this->hasRole(self::ROLE_INSPECTOR);
    }

    public function isSupport(): bool
    {
        return $this->hasRole(self::ROLE_SUPPORT);
    }

    public function isUser(): bool
    {
        return $this->hasRole(self::ROLE_USER);
    }

    public function justUserRole(): bool
    {
        return $this->isUser() && 1 === \count($this->getRoles());
    }

    public function getOpen(): ?bool
    {
        return $this->open;
    }

    public function setOpen(bool $open): self
    {
        $this->open = $open;

        return $this;
    }

    /**
     * @return Collection|Filter[]
     */
    public function getFilter(): Collection
    {
        return $this->filter;
    }

    public function addFilter(Filter $filter): self
    {
        if (!$this->filter->contains($filter)) {
            $this->filter[] = $filter;
        }

        return $this;
    }

    public function removeFilter(Filter $filter): self
    {
        $this->filter->removeElement($filter);

        return $this;
    }

    /**
     * @return Collection|Filter[]
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function addManagerFilter(Filter $filter): self
    {
        if (!$this->filters->contains($filter)) {
            $this->filters[] = $filter;
        }

        return $this;
    }

    public function removeManagerFilter(Filter $filter): self
    {
        $this->filters->removeElement($filter);

        return $this;
    }

    public function getMeta(): ?array
    {
        return $this->meta;
    }

    public function setMeta(?array $meta): self
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * @return $this
     */
    public function setMetaByLabel(string $label, $value): self
    {
        $meta = $this->getMeta();
        $meta[$label] = $value;
        $this->setMeta($meta);

        return $this;
    }

    public function getMetaValueByLabel(string $label)
    {
        //        $meta = json_decode($this->getMeta());
        $meta = $this->getMeta();

        return $meta[$label] ?? null;
    }

    /**
     * @return $this
     */
    public function removeMetaValueByLabel(string $label)
    {
        $meta = $this->getMeta();
        unset($meta[$label]);
        $this->setMeta($meta);

        return $this;
    }

    /**
     * @Groups("user_area")
     *
     * @return string
     */
    public function getPolicies()
    {
        return
            null == $this->getMetaValueByLabel('policiesAccepted')
            || (null != $this->getMetaValueByLabel('inactivityPeriod') && null == $this->getMetaValueByLabel('inactivityPeriodAccepted'));
    }

    /**
     * @param Filter[] $filters
     *
     * @return $this
     */
    public function setFilters($filters)
    {
        $oldFilters = $this->getFilter();
        foreach ($oldFilters as $oldFilter) {
            if (!\in_array($oldFilter, $filters)) {
                $this->removeFilter($oldFilter);
            }
        }

        foreach ($filters as $filter) {
            $this->addFilter($filter);
        }

        return $this;
    }

    public function getAutologinHash()
    {
        return md5($this->getId() . '-' . $this->getEmail() . '-' . date('Ymd'));
    }

    /**
     * @return Collection<int, ItineraryUser>
     */
    public function getItineraryUsers(): Collection
    {
        return $this->itineraryUsers;
    }

    public function addItineraryUser(ItineraryUser $itineraryUser): self
    {
        if (!$this->itineraryUsers->contains($itineraryUser)) {
            $this->itineraryUsers[] = $itineraryUser;
            $itineraryUser->setUser($this);
        }

        return $this;
    }

    public function removeItineraryUser(ItineraryUser $itineraryUser): self
    {
        if ($this->itineraryUsers->removeElement($itineraryUser)) {
            // set the owning side to null (unless already changed)
            if ($itineraryUser->getUser() === $this) {
                $itineraryUser->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @Groups("user_area")
     *
     * @return int
     */
    public function getFinishedCourses()
    {
        $finishedCourses = 0;
        foreach ($this->getCourses() as $startedCourses) {
            if (!\is_null($startedCourses->getFinishedAt()) && !\is_null($startedCourses->getValuedAt())) {
                ++$finishedCourses;
            }
        }

        return $finishedCourses;
    }

    /**
     * @Groups("user_area")
     *
     * @return int|null
     */
    public function getTimeSpent()
    {
        $timeInSeconds = 0;
        foreach ($this->getCourses() as $startedCourses) {
            $timeInSeconds += $startedCourses->getTimeSpent();
        }

        return $timeInSeconds;
    }

    /**
     * @return Collection<int, ItineraryManager>
     */
    public function getItineraryManagers(): Collection
    {
        return $this->itineraryManagers;
    }

    public function addItineraryManager(ItineraryManager $itineraryManager): self
    {
        if (!$this->itineraryManagers->contains($itineraryManager)) {
            $this->itineraryManagers[] = $itineraryManager;
            $itineraryManager->setUser($this);
        }

        return $this;
    }

    public function removeItineraryManager(ItineraryManager $itineraryManager): self
    {
        if ($this->itineraryManagers->removeElement($itineraryManager)) {
            // set the owning side to null (unless already changed)
            if ($itineraryManager->getUser() === $this) {
                $itineraryManager->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Itinerary>
     */
    public function getManagerItineraries(): Collection
    {
        return $this->managerItineraries;
    }

    public function addManagerItinerary(Itinerary $managerItinerary): self
    {
        if (!$this->managerItineraries->contains($managerItinerary)) {
            $this->managerItineraries[] = $managerItinerary;
            $managerItinerary->addManager($this);
        }

        return $this;
    }

    public function removeManagerItinerary(Itinerary $managerItinerary): self
    {
        if ($this->managerItineraries->removeElement($managerItinerary)) {
            $managerItinerary->removeManager($this);
        }

        return $this;
    }

    /**
     * @return Collection|ForumLikes[]
     */
    public function getForumLikes(): Collection
    {
        return $this->forumLikes;
    }

    public function addForumLike(ForumLikes $forumLike): self
    {
        if (!$this->forumLikes->contains($forumLike)) {
            $this->forumLikes[] = $forumLike;
            $forumLike->setUser($this);
        }

        return $this;
    }

    public function removeForumLike(ForumLikes $forumLike): self
    {
        if ($this->forumLikes->removeElement($forumLike)) {
            // set the owning side to null (unless already changed)
            if ($forumLike->getUser() === $this) {
                $forumLike->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ForumReport[]
     */
    public function getForumReports(): Collection
    {
        return $this->forumReports;
    }

    public function addForumReport(ForumReport $forumReport): self
    {
        if (!$this->forumReports->contains($forumReport)) {
            $this->forumReports[] = $forumReport;
            $forumReport->setUser($this);
        }

        return $this;
    }

    public function removeForumReport(ForumReport $forumReport): self
    {
        if ($this->forumReports->removeElement($forumReport)) {
            // set the owning side to null (unless already changed)
            if ($forumReport->getUser() === $this) {
                $forumReport->setUser(null);
            }
        }

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return $this->getEmail();
    }

    /**
     * @Groups("user_area")
     */
    public function hasItineraries(): bool
    {
        $direct = (bool) \count($this->itineraryUsers);

        if ($direct) {
            return true;
        }

        $itineraries = new ArrayCollection();

        foreach ($this->getFilter() as $filter) {
            foreach ($filter->getItineraries() as $itinerary) {
                if (!$itinerary->getId()) {
                    continue;
                }

                if (!$itineraries->contains($itinerary)) {
                    $itineraries->add($itinerary);

                    $differentCategories = [];
                    foreach ($itinerary->getFilters() as $itineraryFilter) {
                        if (!\in_array($itineraryFilter->getFilterCategory(), $differentCategories)) {
                            array_push($differentCategories, $itineraryFilter->getFilterCategory());
                        }
                    }

                    $countFilters = 0;
                    foreach ($this->getFilter() as $userFilter) {
                        if ($itinerary->getFilters()->contains($userFilter)) {
                            ++$countFilters;
                        }
                    }

                    if (\count($differentCategories) <= $countFilters) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function hasItineraryByFilters(Itinerary $itinerary): bool
    {
        $differentCategories = [];
        foreach ($itinerary->getFilters() as $itineraryFilter) {
            if (!\in_array($itineraryFilter->getFilterCategory(), $differentCategories)) {
                array_push($differentCategories, $itineraryFilter->getFilterCategory());
            }
        }

        $countFilters = 0;
        foreach ($this->getFilter() as $userFilter) {
            if ($itinerary->getFilters()->contains($userFilter)) {
                ++$countFilters;
            }
        }

        return \count($differentCategories) <= $countFilters;
    }

    public function getFiltersJson()
    {
        return $this->filtersJson;
    }

    public function setFiltersJson($data)
    {
        $this->filtersJson = $data;
    }

    /**
     * @return Collection<int, UserToken>
     */
    public function getUserTokens(): Collection
    {
        return $this->userTokens;
    }

    public function addUserToken(UserToken $userToken): self
    {
        if (!$this->userTokens->contains($userToken)) {
            $this->userTokens[] = $userToken;
            $userToken->setUser($this);
        }

        return $this;
    }

    public function addMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if (!$this->materialDownloadHistories->contains($materialDownloadHistory)) {
            $this->materialDownloadHistories[] = $materialDownloadHistory;
            $materialDownloadHistory->setUser($this);
        }

        return $this;
    }

    /**
     * @return ArrayCollection|PersistentCollection|null
     */
    public function getMaterialDownloadHistories(): ArrayCollection
    {
        return $this->materialDownloadHistories;
    }

    public function removeMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if ($this->materialDownloadHistories->removeElement($materialDownloadHistory)) {
            // set the owning side to null (unless already changed)
            if ($materialDownloadHistory->getUser() === $this) {
                $materialDownloadHistory->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, HistorySeenMaterial>
     */
    public function getHistorySeenMaterials(): Collection
    {
        return $this->historySeenMaterials;
    }

    public function addHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if (!$this->historySeenMaterials->contains($historySeenMaterial)) {
            $this->historySeenMaterials[] = $historySeenMaterial;
            $historySeenMaterial->setUser($this);
        }

        return $this;
    }

    public function removeHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if ($this->historySeenMaterials->removeElement($historySeenMaterial)) {
            // set the owning side to null (unless already changed)
            if ($historySeenMaterial->getUser() === $this) {
                $historySeenMaterial->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, TaskUser>
     */
    public function getTaskUsers(): Collection
    {
        return $this->taskUsers;
    }

    public function addTaskUser(TaskUser $taskUser): self
    {
        if (!$this->taskUsers->contains($taskUser)) {
            $this->taskUsers[] = $taskUser;
            $taskUser->setUser($this);
        }

        return $this;
    }

    public function removeTaskUser(TaskUser $taskUser): self
    {
        if ($this->taskUsers->removeElement($taskUser)) {
            // set the owning side to null (unless already changed)
            if ($taskUser->getUser() === $this) {
                $taskUser->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, LibraryViews>
     */
    public function getLibraryViews(): Collection
    {
        return $this->libraryViews;
    }

    public function addLibraryView(LibraryViews $libraryView): self
    {
        if (!$this->libraryViews->contains($libraryView)) {
            $this->libraryViews[] = $libraryView;
            $libraryView->setUser($this);
        }

        return $this;
    }

    public function removeLibraryView(LibraryViews $libraryView): self
    {
        if ($this->libraryViews->removeElement($libraryView)) {
            // set the owning side to null (unless already changed)
            if ($libraryView->getUser() === $this) {
                $libraryView->setUser(null);
            }
        }

        return $this;
    }

    public function removeUserToken(UserToken $userToken): self
    {
        if ($this->userTokens->removeElement($userToken)) {
            // set the owning side to null (unless already changed)
            if ($userToken->getUser() === $this) {
                $userToken->setUser(null);
            }
        }

        return $this;
    }

    public function getTeamManager(): ?self
    {
        return $this->teamManager;
    }

    public function setTeamManager(?self $teamManager): self
    {
        $this->teamManager = $teamManager;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getTeamUsers(): Collection
    {
        return $this->teamUsers;
    }

    public function addTeamUser(self $teamUser): self
    {
        if (!$this->teamUsers->contains($teamUser)) {
            $this->teamUsers[] = $teamUser;
            $teamUser->setTeamManager($this);
        }

        return $this;
    }

    public function removeTeamUser(self $teamUser): self
    {
        if ($this->teamUsers->removeElement($teamUser)) {
            // set the owning side to null (unless already changed)
            if ($teamUser->getTeamManager() === $this) {
                $teamUser->setTeamManager(null);
            }
        }

        return $this;
    }

    public function getTeamManagerEmail(): ?string
    {
        return $this->teamManagerEmail;
    }

    public function setTeamManagerEmail(?string $teamManagerEmail): self
    {
        $this->teamManagerEmail = $teamManagerEmail;

        return $this;
    }

    /**
     * @return Collection|UserVcmsProject[]
     */
    public function getVcmsProjects(): Collection
    {
        return $this->vcmsProjects;
    }

    public function addVcmsProject(UserVcmsProject $vcmsProject): self
    {
        if (!$this->vcmsProjects->contains($vcmsProject)) {
            $this->vcmsProjects[] = $vcmsProject;
            $vcmsProject->setUser($this);
        }

        return $this;
    }

    public function removeVcmsProject(UserVcmsProject $vcmsProject): self
    {
        if ($this->vcmsProjects->removeElement($vcmsProject)) {
            // set the owning side to null (unless already changed)
            if ($vcmsProject->getUser() === $this) {
                $vcmsProject->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserTime>
     */
    public function getUserTimes(): Collection
    {
        return $this->userTimes;
    }

    public function addUserTime(UserTime $userTime): self
    {
        if (!$this->userTimes->contains($userTime)) {
            $this->userTimes[] = $userTime;
            $userTime->setUser($this);
        }

        return $this;
    }

    public function removeUserTime(UserTime $userTime): self
    {
        if ($this->userTimes->removeElement($userTime)) {
            // set the owning side to null (unless already changed)
            if ($userTime->getUser() === $this) {
                $userTime->setUser(null);
            }
        }

        return $this;
    }

    public function getUserCoursesTotalTime(): ?UserCoursesTotalTime
    {
        return $this->userCoursesTotalTime;
    }

    public function setUserCoursesTotalTime(UserCoursesTotalTime $userCoursesTotalTime): self
    {
        // set the owning side of the relation if necessary
        if ($userCoursesTotalTime->getUser() !== $this) {
            $userCoursesTotalTime->setUser($this);
        }

        $this->userCoursesTotalTime = $userCoursesTotalTime;

        return $this;
    }

    public function isStarteam(): ?bool
    {
        return $this->starteam;
    }

    public function setStarteam(bool $starteam): self
    {
        $this->starteam = $starteam;

        return $this;
    }

    /**
     * Get the value of emailNotificationUser.
     */
    public function getEmailNotificationUser()
    {
        return $this->emailNotificationUser;
    }

    /**
     * Set the value of emailNotificationUser.
     *
     * @return self
     */
    public function setEmailNotificationUser($emailNotificationUser)
    {
        $this->emailNotificationUser = $emailNotificationUser;

        return $this;
    }

    /**
     * Get the value of userNotification.
     */
    public function getUserNotification()
    {
        return $this->userNotification;
    }

    /**
     * Set the value of userNotification.
     *
     * @return self
     */
    public function setUserNotification($userNotification)
    {
        $this->userNotification = $userNotification;

        return $this;
    }

    /**
     * @return Collection<int, ChatMessage>
     */
    public function getChatMessages(): Collection
    {
        return $this->chatMessages;
    }

    public function addChatMessage(ChatMessage $chatMessage): self
    {
        if (!$this->chatMessages->contains($chatMessage)) {
            $this->chatMessages[] = $chatMessage;
            $chatMessage->setUser($this);
        }

        return $this;
    }

    public function removeChatMessage(ChatMessage $chatMessage): self
    {
        if ($this->chatMessages->removeElement($chatMessage)) {
            // set the owning side to null (unless already changed)
            if ($chatMessage->getUser() === $this) {
                $chatMessage->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ChatChannelUser>
     */
    public function getChatChannelUsers(): Collection
    {
        return $this->chatChannelUsers;
    }

    public function addChatChannelUser(ChatChannelUser $chatChannelUser): self
    {
        if (!$this->chatChannelUsers->contains($chatChannelUser)) {
            $this->chatChannelUsers[] = $chatChannelUser;
            $chatChannelUser->setUser($this);
        }

        return $this;
    }

    public function removeChatChannelUser(ChatChannelUser $chatChannelUser): self
    {
        if ($this->chatChannelUsers->removeElement($chatChannelUser)) {
            // set the owning side to null (unless already changed)
            if ($chatChannelUser->getUser() === $this) {
                $chatChannelUser->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ChatMessageLike>
     */
    public function getChatMessageLikes(): Collection
    {
        return $this->chatMessageLikes;
    }

    public function addChatMessageLike(ChatMessageLike $chatMessageLike): self
    {
        if (!$this->chatMessageLikes->contains($chatMessageLike)) {
            $this->chatMessageLikes[] = $chatMessageLike;
            $chatMessageLike->setUser($this);
        }

        return $this;
    }

    public function removeChatMessageLike(ChatMessageLike $chatMessageLike): self
    {
        if ($this->chatMessageLikes->removeElement($chatMessageLike)) {
            // set the owning side to null (unless already changed)
            if ($chatMessageLike->getUser() === $this) {
                $chatMessageLike->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ChatMessageReport>
     */
    public function getChatMessageReports(): Collection
    {
        return $this->chatMessageReports;
    }

    public function addChatMessageReport(ChatMessageReport $chatMessageReport): self
    {
        if (!$this->chatMessageReports->contains($chatMessageReport)) {
            $this->chatMessageReports[] = $chatMessageReport;
            $chatMessageReport->setUser($this);
        }

        return $this;
    }

    public function removeChatMessageReport(ChatMessageReport $chatMessageReport): self
    {
        if ($this->chatMessageReports->removeElement($chatMessageReport)) {
            // set the owning side to null (unless already changed)
            if ($chatMessageReport->getUser() === $this) {
                $chatMessageReport->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserRoleplayProject>
     */
    public function getRoleplayProjects(): Collection
    {
        return $this->roleplayProjects;
    }

    public function addRoleplayProject(UserRoleplayProject $roleplayProject): self
    {
        if (!$this->roleplayProjects->contains($roleplayProject)) {
            $this->roleplayProjects[] = $roleplayProject;
            $roleplayProject->setUser($this);
        }

        return $this;
    }

    public function removeRoleplayProject(UserRoleplayProject $roleplayProject): self
    {
        if ($this->roleplayProjects->removeElement($roleplayProject)) {
            // set the owning side to null (unless already changed)
            if ($roleplayProject->getUser() === $this) {
                $roleplayProject->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, GenericToken>
     */
    public function getGenericTokens(): Collection
    {
        return $this->genericTokens;
    }

    public function addGenericToken(GenericToken $genericToken): self
    {
        if (!$this->genericTokens->contains($genericToken)) {
            $this->genericTokens[] = $genericToken;
            $genericToken->setCreatedBy($this);
        }

        return $this;
    }

    public function getCustomFilters(): ?array
    {
        return $this->customFilters;
    }

    public function setCustomFilters(array $customFilters): self
    {
        $this->customFilters = $customFilters;

        return $this;
    }

    public function removeGenericToken(GenericToken $genericToken): self
    {
        if ($this->genericTokens->removeElement($genericToken)) {
            // set the owning side to null (unless already changed)
            if ($genericToken->getCreatedBy() === $this) {
                $genericToken->setCreatedBy(null);
            }
        }

        return $this;
    }

    public function hasFilterId($id): bool
    {
        foreach ($this->getFilter() as $filter) {
            if ($filter->getId() == $id) {
                return true;
            }
        }

        return false;
    }

    public function addCustomAssignedFilter($id): self
    {
        if (!$this->isCustomAssignedFilter($id)) {
            $customFilters = $this->getCustomFilters();
            $customFilters['assigned'][] = $id;
            $this->setCustomFilters($customFilters);
        }

        return $this;
    }

    public function getUserFieldsFundae(): ?UserFieldsFundae
    {
        return $this->userFieldsFundae;
    }

    public function setUserFieldsFundae(UserFieldsFundae $userFieldsFundae): self
    {
        // set the owning side of the relation if necessary
        if ($userFieldsFundae->getUser() !== $this) {
            $userFieldsFundae->setUser($this);
        }

        $this->userFieldsFundae = $userFieldsFundae;

        return $this;
    }

    public function addCustomExcludedFilter($id): self
    {
        if (!$this->isCustomAssignedFilter($id)) {
            $customFilters = $this->getCustomFilters();
            $customFilters['excluded'][] = $id;
            $this->setCustomFilters($customFilters);
        }

        return $this;
    }

    public function removeCustomFilter($id): self
    {
        $customFilters = $this->getCustomFilters();

        if (!empty($customFilters['assigned'])) {
            foreach ($customFilters['assigned'] as $key => $filterId) {
                if ($filterId == $id) {
                    unset($customFilters['assigned'][$key]);
                }
            }
            $customFilters['assigned'] = array_values($customFilters['assigned']);
        }

        if (!empty($customFilters['excluded'])) {
            foreach ($customFilters['excluded'] as $key => $filterId) {
                if ($filterId == $id) {
                    unset($customFilters['excluded'][$key]);
                }
            }
            $customFilters['excluded'] = array_values($customFilters['excluded']);
        }

        $this->setCustomFilters($customFilters);

        return $this;
    }

    public function isCustomAssignedFilter($id): bool
    {
        $customFilters = $this->getCustomFilters();
        if (empty($customFilters['assigned'])) {
            return false;
        }

        foreach ($customFilters['assigned'] as $filterId) {
            if ($filterId == $id) {
                return true;
            }
        }

        return false;
    }

    public function isCustomExcludedFilter($id): bool
    {
        $customFilters = $this->getCustomFilters();
        if (empty($customFilters['excluded'])) {
            return false;
        }

        foreach ($customFilters['excluded'] as $filterId) {
            if ($filterId == $id) {
                return true;
            }
        }

        return false;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getLocalFilters(): array
    {
        $localFilters = [];
        $currentFilters = $this->getFilter()->toArray();
        /** @var Filter $f */
        foreach ($currentFilters as $f) {
            if (Filter::SOURCE_REMOTE !== $f->getSource()) {
                $localFilters[] = $f;
            }
        }

        return $localFilters;
    }

    public static function allowedUpdatableSSOFields(): array
    {
        $reflection = new \ReflectionClass(User::class);
        $properties = $reflection->getDefaultProperties();
        unset($properties['id']);
        unset($properties['password']);
        unset($properties['newPassword']);
        unset($properties['filtersJson']);
        unset($properties['createdAt']);
        unset($properties['createdBy']);
        unset($properties['updatedAt']);
        unset($properties['updatedBy']);
        unset($properties['deletedAt']);
        unset($properties['deletedBy']);
        unset($properties['customFilters']);
        unset($properties['roles']);
        unset($properties['remoteRoles']);
        unset($properties['announcements']);
        unset($properties['courses']);
        unset($properties['userComments']);
        unset($properties['messagesSent']);
        unset($properties['messagesReceived']);
        unset($properties['notifications']);
        unset($properties['userLogins']);
        unset($properties['forumPosts']);
        unset($properties['tutorships']);
        unset($properties['nps']);
        unset($properties['managedCourses']);
        unset($properties['emailsReceived']);
        unset($properties['challengeUsers']);
        unset($properties['challengeUserPoints']);
        unset($properties['challengeDuels']);
        unset($properties['filter']);
        unset($properties['filters']);
        unset($properties['vcmsProjects']);
        unset($properties['itineraryUsers']);
        unset($properties['itineraryManagers']);
        unset($properties['managerItineraries']);
        unset($properties['userTokens']);
        unset($properties['forumLikes']);
        unset($properties['forumReports']);
        unset($properties['materialDownloadHistories']);
        unset($properties['challengeDuelQuestionsAdns']);
        unset($properties['historySeenMaterials']);
        unset($properties['taskUsers']);
        unset($properties['libraryViews']);
        unset($properties['teamUsers']);
        unset($properties['userTimes']);
        unset($properties['roleplayProjects']);

        return array_keys($properties);
    }

    /**
     * @return Collection<int, UserIdentification>
     */
    public function getUserIdentifications(): Collection
    {
        return $this->userIdentifications;
    }

    public function addUserIdentification(UserIdentification $userIdentification): self
    {
        if (!$this->userIdentifications->contains($userIdentification)) {
            $this->userIdentifications[] = $userIdentification;
            $userIdentification->setUser($this);
        }

        return $this;
    }

    public function removeUserIdentification(UserIdentification $userIdentification): self
    {
        if ($this->userIdentifications->removeElement($userIdentification)) {
            // set the owning side to null (unless already changed)
            if ($userIdentification->getUser() === $this) {
                $userIdentification->setUser(null);
            }
        }

        return $this;
    }

    public function getLocaleCampus(): ?string
    {
        return $this->localeCampus ?? $this->locale;
    }

    public function setLocaleCampus(?string $localeCampus): self
    {
        $this->localeCampus = $localeCampus;

        return $this;
    }

    public static function generatePassword($length = 10): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = \strlen($characters);
        $randomString = '';
        $length = $length >= 4 ? $length : 10;

        for ($i = 0; $i < $length; ++$i) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
    }
}
