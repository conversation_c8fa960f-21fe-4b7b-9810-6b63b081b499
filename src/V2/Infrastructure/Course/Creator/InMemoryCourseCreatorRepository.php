<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Course\Creator;

use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\CourseCreatorRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryCourseCreatorRepository implements CourseCreatorRepository
{
    private CourseCreatorCollection $courseCreators;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->courseCreators = new CourseCreatorCollection([]);
    }

    #[\Override]
    public function insert(CourseCreator $courseCreator): void
    {
        try {
            $this->findOneBy(
                CourseCreatorCriteria::createEmpty()
                    ->filterByCourseId($courseCreator->getCourseId())
                    ->filterByCourseId($courseCreator->getCourseId()),
            );

            throw CourseCreatorRepositoryException::duplicateCourseCreator($courseCreator);
        } catch (CourseCreatorNotFoundException) {
            $this->courseCreators->append(clone $courseCreator);
        }
    }

    #[\Override]
    public function findOneBy(CourseCreatorCriteria $criteria): CourseCreator
    {
        $creators = $this->filterByCriteria($criteria);

        if (empty($creators)) {
            throw new CourseCreatorNotFoundException();
        }

        return reset($creators);
    }

    #[\Override]
    public function findBy(CourseCreatorCriteria $criteria): CourseCreatorCollection
    {
        return new CourseCreatorCollection(
            array_values($this->filterByCriteria($criteria))
        );
    }

    #[\Override]
    public function delete(CourseCreator $courseCreator): void
    {
        $this->courseCreators = $this->courseCreators->filter(
            fn (CourseCreator $creator) => $creator->getUserId() !== $courseCreator->getUserId()
                || $creator->getCourseId() !== $courseCreator->getCourseId()
        );
    }

    private function filterByCriteria(CourseCreatorCriteria $criteria): array
    {
        $creators = array_filter(
            $this->courseCreators->all(),
            fn (CourseCreator $courseCreator) => (null === $criteria->getCourseId() || $courseCreator->getCourseId() === $criteria->getCourseId())
            && (null === $criteria->getUserId() || $courseCreator->getUserId() === $criteria->getUserId())
        );

        return array_map(
            fn (CourseCreator $courseCreator) => new CourseCreator(
                userId: $courseCreator->getUserId(),
                courseId: $courseCreator->getCourseId(),
            ),
            $creators
        );
    }
}
