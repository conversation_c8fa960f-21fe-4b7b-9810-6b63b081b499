<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI\Exceptions;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class LtiException extends InfrastructureException
{
    protected const int CODE = 108116105; // LTI in ASCII

    public static function clientIdMustBeUnique(): self
    {
        return new self(
            message: 'Client ID must be unique',
            code: self::CODE,
        );
    }
}
