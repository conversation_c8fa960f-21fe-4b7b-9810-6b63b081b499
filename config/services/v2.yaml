services:
  App\V2\Infrastructure\Controller\Admin\:
    resource: '../../src/V2/Infrastructure/Controller/Admin/'
    tags: [ 'controller.service_arguments' ]

  # V2 Domain repositories
  App\V2\Domain\User\UserRepository:
    class: App\V2\Infrastructure\Persistence\User\DoctrineUserRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Course\Creator\DBALCourseCreatorRepository

  # Hydrators
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator:
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection:
    arguments:
      - - '@App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator'