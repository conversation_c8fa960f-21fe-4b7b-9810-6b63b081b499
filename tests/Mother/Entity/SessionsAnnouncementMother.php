<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\SessionsAnnouncement;

class SessionsAnnouncementMother
{
    public static function create(
        ?int               $id = null,
        ?Announcement      $announcement = null,
        ?int               $hourSession = null,
        ?string            $nameAnnouncement = null,
        ?string            $classroom = null,
        ?int               $numSessions = null,
        ?string            $extra = null,
        ?string            $assistance = null,
        ?AnnouncementGroup $announcementGroup = null,

    ): SessionsAnnouncement
    {
        $sessionAnnouncement = new SessionsAnnouncement();

        if (null !== $id) {
            $sessionAnnouncement->setId($id);
        }

        $sessionAnnouncement->setAnnouncement($announcement)
            ->setHourSession($hourSession)
            ->setNameAnnouncement($nameAnnouncement)
            ->setClassroom($classroom)
            ->setNumSessions($numSessions)
            ->setExtra($extra)
            ->setAssistance($assistance)
            ->setAnnouncementGroup($announcementGroup);

        return $sessionAnnouncement;
    }
}
