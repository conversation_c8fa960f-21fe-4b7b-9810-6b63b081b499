<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\ClassroomvirtualUser;
use App\Entity\User;
use App\Entity\UserCompany;
use App\Entity\UserProfessionalCategory;
use App\Entity\UserStudyLevel;
use App\Entity\UserWorkCenter;
use App\Entity\UserWorkDepartment;

class AnnouncementUserMother
{
    public static function create(
        ?int                              $id = null,
        ?Announcement                     $announcement = null,
        ?User                             $user = null,
        ?\DateTimeImmutable               $notified = null,
        ?AnnouncementGroup                $announcementGroup = null,
        ?bool                             $isApproved = null,
        ?\DateTimeImmutable               $dateApproved = null,
        ?bool                             $isDownloadDiploma = null,
        ?bool                             $isReadDidacticGuide = null,
        ?\DateTimeImmutable               $dateReadDidacticGuide = null,
        ?bool                             $isDownloadDidacticGuide = null,
        ?\DateTimeImmutable               $dateDownloadDidacticGuide = null,
        ?ClassRoomvirtualUser             $classroomvirtualUsers = null,
        ?\DateTimeImmutable               $valuedCourseAt = null,
        ?UserCompany                      $userCompany = null,
        ?UserProfessionalCategory         $userProfessionalCategory = null,
        ?UserWorkCenter                   $userWorkCenter = null,
        ?UserWorkDepartment               $userWorkDepartment = null,
        ?UserStudyLevel                   $userStudyLevel = null,
        ?bool                             $isConfirmationAssistance = null,
        ?\DateTimeInterface               $dateConfirmationAssistance = null,
        ?bool                             $external = null,
        ?AnnouncementUserDigitalSignature $announcementGroupSession = null,
    ): AnnouncementUser
    {
        $announcementUser = new AnnouncementUser();

        if (null !== $id) {
            $announcementUser->setId($id);
        }

        $announcementUser->setAnnouncement($announcement)
            ->setUser($user)
            ->setNotified($notified ?? new \DateTimeImmutable())
            ->setannouncementGroup($announcementGroup)
            ->setAproved($isApproved ?? false)
            ->setDateApproved($dateApproved ?? new \DateTimeImmutable())
            ->setDownloadDiploma($isDownloadDiploma ?? false)
            ->setReadDidacticGuide($isReadDidacticGuide ?? false)
            ->setDateReadDidacticGuide($dateReadDidacticGuide ?? new \DateTimeImmutable())
            ->setDownloadDidacticGuide($isDownloadDidacticGuide ?? false)
            ->setDateDownloadDidacticGuide($dateDownloadDidacticGuide ?? new \DateTimeImmutable())
            ->setValuedCourseAt($valuedCourseAt)
            ->setUserCompany($userCompany)
            ->setUserProfessionalCategory($userProfessionalCategory)
            ->setUserWorkCenter($userWorkCenter)
            ->setUserWorkDepartment($userWorkDepartment)
            ->setUserStudyLevel($userStudyLevel)
            ->setIsConfirmationAssistance($isConfirmationAssistance ?? false)
            ->setDateConfirmationAssistance($dateConfirmationAssistance ?? new \DateTimeImmutable())
            ->setExternal($external ?? false)
            ->setAnnouncementGroupSession($announcementGroupSession);

        if (null !== $classroomvirtualUsers) {
            foreach ($classroomvirtualUsers as $classroomvirtualUser) {
                $announcementUser->addClassroomvirtualUser($classroomvirtualUser);
            }
        }

        return $announcementUser;
    }
}
