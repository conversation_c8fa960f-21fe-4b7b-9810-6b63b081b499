<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementGroupSessionAssistanceFiles;
use App\Entity\AnnouncementModality;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\Classroomvirtual;
use App\Entity\TypeMoney;

class AnnouncementGroupSessionsMother
{
    public static function create(
        ?int                  $id = null,
        ?AnnouncementGroup    $announcementGroup = null,
        ?\DateTimeImmutable   $startAt = null,
        ?\DateTimeImmutable   $finishAt = null,
        ?string               $url = null,
        ?array                $assistance = [],
        ?int                  $sessionNumber = null,
        ?Classroomvirtual     $classroomvirtual = null,
        ?array                $studentAssistance = [],
        ?array                $assistanceFiles = [],
        ?int                  $entryMargin = null,
        ?int                  $exitMargin = null,
        ?string               $timezone = null,
        ?string               $place = null,
        ?array                $announcementUserDigitalSignatures = [],
        ?string               $cost = null,
        ?Typemoney            $typeMoney = null,
        ?string               $type = null,
        ?AnnouncementModality $modality = null

    ): AnnouncementGroupSession
    {
        $announcementGroupSession = new AnnouncementGroupSession();

        if (null !== $id) {
            $announcementGroupSession->setId($id);
        }

        $announcementGroupSession->setAnnouncementGroup($announcementGroup)
            ->setStartAt($startAt ?? new \DateTimeImmutable())
            ->setFinishAt($finishAt ?? new \DateTimeImmutable())
            ->setUrl($url)
            ->setAssistance($assistance)
            ->setSessionNumber($sessionNumber)
            ->setClassroomvirtual($classroomvirtual)
            ->setStudentAssistance($studentAssistance)
            ->setEntryMargin($entryMargin)
            ->setExitMargin($exitMargin)
            ->setTimezone($timezone)
            ->setPlace($place)
            ->setCost($cost)
            ->setTypeMoney($typeMoney)
            ->setType($type)
            ->setModality($modality);


        foreach ($assistanceFiles as $assistanceFile) {
            $announcementGroupSession->addAssistanceFile($assistanceFile);
        }

        foreach ($announcementUserDigitalSignatures as $announcementUserDigitalSignature) {
            $announcementGroupSession->addAnnouncementUserDigitalSignature($announcementUserDigitalSignature);
        }

        return $announcementGroupSession;
    }
}
