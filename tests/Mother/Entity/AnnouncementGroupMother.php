<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementNotificationGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Classroomvirtual;
use App\Entity\SessionsAnnouncement;
use App\Entity\TaskCourseGroup;
use App\Entity\TypeMoney;

class AnnouncementGroupMother
{
    public static function create(
        ?int                  $id = null,
        ?string               $companyProfile = null,
        ?string               $code = null,
        ?string               $companyCif = null,
        ?string               $denomination = null,
        ?string               $fileNumber = null,
        ?array                $announcementUsers = [],
        ?Announcement         $announcement = null,
        ?AnnouncementTutor    $announcementTutor = null,
        ?int                  $numSessions = null,
        ?string               $place = null,
        ?SessionsAnnouncement $sessionsAnnouncement = null,
        ?array                $announcementGroupSessions = [],
        ?array                $classroomvirtuals = [],
        ?array                $taskCourseGroups = [],
        ?array                $announcementNotificationGroups = [],
        ?int                  $groupNumber = null,
        ?string               $cost = null,
        ?TypeMoney            $typeMoney = null
    ): AnnouncementGroup
    {
        $announcementGroup = new AnnouncementGroup();

        if (null !== $id) {
            $announcementGroup->setId($id);
        }

        $announcementGroup->setCompanyProfile($companyProfile)
            ->setCode($code)
            ->setCompanyCif($companyCif)
            ->setDenomination($denomination)
            ->setFileNumber($fileNumber)
            ->setNumSessions($numSessions)
            ->setPlace($place)
            ->setGroupNumber($groupNumber)
            ->setCost($cost)
            ->setTypeMoney($typeMoney)
            ->setAnnouncement($announcement)
            ->setAnnouncementTutor($announcementTutor)
            ->setSessionsAnnouncement($sessionsAnnouncement);

        foreach ($announcementUsers as $announcementUser) {
            $announcementGroup->setAnnouncementUsers($announcementUser);
        }

        foreach ($announcementGroupSessions as $announcementGroupSession) {
            $announcementGroup->addAnnouncementGroupSession($announcementGroupSession);
        }

        foreach ($classroomvirtuals as $classroomvirtual) {
            $announcementGroup->addClassroomvirtual($classroomvirtual);
        }

        foreach ($taskCourseGroups as $taskCourseGroup) {
            $announcementGroup->addTaskCourseGroup($taskCourseGroup);
        }

        foreach ($announcementNotificationGroups as $announcementNotificationGroup) {
            $announcementGroup->addAnnouncementNotificationGroup($announcementNotificationGroup);
        }

        return $announcementGroup;
    }
}
