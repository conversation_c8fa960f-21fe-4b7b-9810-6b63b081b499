<?php

namespace App\Tests\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Repository\AnnouncementRepository;
use App\Service\Annoucement\Admin\AnnouncementAlertTutorService;
use App\Service\Annoucement\Admin\AnnouncementAprovedCriteriaService;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\Admin\TaskUserService;
use App\Service\Annoucement\Admin\UserChapterService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementGroupMother;
use App\Tests\Mother\Entity\AnnouncementGroupSessionsMother;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\AnnouncementUserMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\SessionsAnnouncementMother;
use App\Tests\Mother\Entity\TypeCourseMother;
use App\Tests\Mother\Entity\UserMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class AnnouncementUserServiceTest extends TestCase
{
    private AnnouncementUserService $announcementUserService;
    private EntityManagerInterface $entityManager;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    private AnnouncementAlertTutorService $announcementAlertTutorService;
    private TaskUserService $taskUserService;
    private UserChapterService $userChapterService;
    private AnnouncementAprovedCriteriaService $announcementApprovedCriteriaService;
    private SettingsService $settings;


    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $this->announcementAlertTutorService = $this->createMock(AnnouncementAlertTutorService::class);
        $this->taskUserService = $this->createMock(TaskUserService::class);
        $this->userChapterService = $this->createMock(UserChapterService::class);
        $this->announcementApprovedCriteriaService = $this->createMock(AnnouncementAprovedCriteriaService::class);
        $this->settings = $this->createMock(SettingsService::class);
    }

    /**
     * @throws Exception
     */
    public function testVerifyIfAprovedUserAnnouncementPresentialAndVirtual(): void
    {
        $typeCourse = TypeCourseMother::create(
            id: 1,
            name: 'Presencial',
            code: 'presencial',
            denomination: 'INTERN',
        );
        $course = CourseMother::create(
            id: 1,
            typeCourse: $typeCourse,
        );
        $announcement = AnnouncementMother::create(
            id: 1,
            course: $course
        );
        $user = UserMother::create(
            id: 1,
            email: '<EMAIL>'
        );
        $groupSession1 = AnnouncementGroupSessionsMother::create(
            id: 1,
            startAt: new \DateTimeImmutable('today'),
            finishAt: new \DateTimeImmutable('tomorrow'),
            sessionNumber: 1
        );
        $groupSession2 = AnnouncementGroupSessionsMother::create(
            id: 2,
            startAt: new \DateTimeImmutable('today'),
            finishAt: new \DateTimeImmutable('tomorrow'),
            sessionNumber: 2
        );
        $announcementGroup = AnnouncementGroupMother::create(
            id: 1,
            code: 'group-1',
            announcement: $announcement,
            announcementGroupSessions: [$groupSession1, $groupSession2],
            groupNumber: 1
        );
        $announcementUser = AnnouncementUserMother::create(
            id: 1,
            announcement: $announcement,
            user: $user,
            announcementGroup: $announcementGroup
        );

        $this->entityManager->expects($this->once())
            ->method('persist')
            ->with($announcementUser);
        $this->entityManager->expects($this->once())
            ->method('flush');

        $announcementRepositoryMock = $this->createMock(AnnouncementRepository::class);
        $announcementRepositoryMock->expects($this->once())
            ->method('findAnnouncementUserByGroup')
            ->with($announcementGroup->getId(), $announcement->getId())
            ->willReturn([$user]);

        $this->entityManager->expects($this->atLeast(2))
            ->method('getRepository')
            ->with(Announcement::class)
            ->willReturn($announcementRepositoryMock);

        $this->announcementUserService = new AnnouncementUserService(
            $this->entityManager,
            $this->announcementConfigurationsService,
            $this->announcementAlertTutorService,
            $this->taskUserService,
            $this->userChapterService,
            $this->announcementApprovedCriteriaService,
            $this->settings
        );
        $this->announcementUserService->verifyIfAprovedUserAnnouncementPresentialAndVirtual($announcementUser);
    }
}
